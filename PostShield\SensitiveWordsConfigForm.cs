using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace PostShield
{
    public partial class SensitiveWordsConfigForm : Form
    {
        private string configFilePath;
        private List<SensitiveWord> sensitiveWords;

        public SensitiveWordsConfigForm()
        {
            InitializeComponent();
            configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "SensitiveWords.xml");
            LoadSensitiveWords();
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dataGridView1.AutoGenerateColumns = false;
            dataGridView1.Columns.Clear();

            // 添加列
            var textColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Text",
                HeaderText = "敏感词",
                Name = "Text",
                Width = 150
            };
            dataGridView1.Columns.Add(textColumn);

            var categoryColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Category",
                HeaderText = "分类",
                Name = "Category",
                Width = 120
            };
            dataGridView1.Columns.Add(categoryColumn);

            var descriptionColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Description",
                HeaderText = "描述",
                Name = "Description",
                Width = 200
            };
            dataGridView1.Columns.Add(descriptionColumn);

            var enabledColumn = new DataGridViewCheckBoxColumn
            {
                DataPropertyName = "Enabled",
                HeaderText = "启用",
                Name = "Enabled",
                Width = 60
            };
            dataGridView1.Columns.Add(enabledColumn);

            // 绑定数据源
            RefreshDataGridView();
        }

        private void RefreshDataGridView()
        {
            dataGridView1.DataSource = null;
            dataGridView1.DataSource = sensitiveWords;
        }

        private void LoadSensitiveWords()
        {
            sensitiveWords = new List<SensitiveWord>();

            try
            {
                if (!File.Exists(configFilePath))
                {
                    // 如果配置文件不存在，创建默认配置
                    CreateDefaultConfig();
                }

                var doc = XDocument.Load(configFilePath);
                foreach (var wordElement in doc.Descendants("Word"))
                {
                    var word = new SensitiveWord
                    {
                        Text = wordElement.Element("Text")?.Value ?? "",
                        Category = wordElement.Element("Category")?.Value ?? "其他",
                        Description = wordElement.Element("Description")?.Value ?? "",
                        Enabled = bool.Parse(wordElement.Element("Enabled")?.Value ?? "true")
                    };
                    sensitiveWords.Add(word);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载敏感词配置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                CreateDefaultConfig();
            }
        }



        private void CreateDefaultConfig()
        {
            sensitiveWords = new List<SensitiveWord>
            {
                new SensitiveWord { Text = "客户", Category = "客户信息", Description = "客户相关信息", Enabled = true },
                new SensitiveWord { Text = "用户", Category = "客户信息", Description = "用户相关信息", Enabled = true },
                new SensitiveWord { Text = "个人信息", Category = "隐私信息", Description = "个人隐私相关信息", Enabled = true },
                new SensitiveWord { Text = "名单", Category = "客户信息", Description = "名单相关信息", Enabled = true },
                new SensitiveWord { Text = "消费者", Category = "客户信息", Description = "消费者相关信息", Enabled = true },
                new SensitiveWord { Text = "贷款", Category = "金融信息", Description = "贷款相关信息", Enabled = true },
                new SensitiveWord { Text = "账户", Category = "金融信息", Description = "账户相关信息", Enabled = true },
                new SensitiveWord { Text = "放款", Category = "金融信息", Description = "放款相关信息", Enabled = true },
                new SensitiveWord { Text = "交易", Category = "金融信息", Description = "交易相关信息", Enabled = true },
                new SensitiveWord { Text = "邮盾", Category = "系统标识", Description = "邮盾系统标识", Enabled = true }
            };
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtText.Text))
            {
                MessageBox.Show("请输入敏感词", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (sensitiveWords.Any(w => w.Text.Equals(txtText.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("该敏感词已存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var newWord = new SensitiveWord
            {
                Text = txtText.Text.Trim(),
                Category = txtCategory.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                Enabled = chkEnabled.Checked
            };

            sensitiveWords.Add(newWord);
            RefreshDataGridView();

            // 清空输入框
            txtText.Clear();
            txtCategory.Clear();
            txtDescription.Clear();
            chkEnabled.Checked = true;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dataGridView1.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择要删除的敏感词", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("确定要删除选中的敏感词吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                var selectedWord = dataGridView1.SelectedRows[0].DataBoundItem as SensitiveWord;
                if (selectedWord != null)
                {
                    sensitiveWords.Remove(selectedWord);
                    RefreshDataGridView();
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SaveSensitiveWords();
                MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void SaveSensitiveWords()
        {
            var doc = new XDocument(
                new XElement("SensitiveWords",
                    sensitiveWords.Select(word =>
                        new XElement("Word",
                            new XElement("Text", word.Text),
                            new XElement("Category", word.Category),
                            new XElement("Description", word.Description),
                            new XElement("Enabled", word.Enabled)
                        )
                    )
                )
            );

            // 确保目录存在
            Directory.CreateDirectory(Path.GetDirectoryName(configFilePath));
            doc.Save(configFilePath);
        }
    }

    public class SensitiveWord
    {
        public string Text { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public bool Enabled { get; set; }
    }
}
