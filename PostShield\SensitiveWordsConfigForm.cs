using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace PostShield
{
    public partial class SensitiveWordsConfigForm : Form
    {
        private string configFilePath;
        private List<SensitiveWord> sensitiveWords;

        public SensitiveWordsConfigForm()
        {
            InitializeComponent();
            configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "SensitiveWords.xml");
            LoadSensitiveWords();
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dataGridView1.AutoGenerateColumns = false;
            dataGridView1.Columns.Clear();

            // 添加列
            var textColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Text",
                HeaderText = "敏感词",
                Name = "Text",
                Width = 150
            };
            dataGridView1.Columns.Add(textColumn);

            var categoryColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Category",
                HeaderText = "分类",
                Name = "Category",
                Width = 120
            };
            dataGridView1.Columns.Add(categoryColumn);

            var descriptionColumn = new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Description",
                HeaderText = "描述",
                Name = "Description",
                Width = 200
            };
            dataGridView1.Columns.Add(descriptionColumn);

            var enabledColumn = new DataGridViewCheckBoxColumn
            {
                DataPropertyName = "Enabled",
                HeaderText = "启用",
                Name = "Enabled",
                Width = 60
            };
            dataGridView1.Columns.Add(enabledColumn);

            // 绑定数据源
            RefreshDataGridView();
        }

        private void RefreshDataGridView()
        {
            dataGridView1.DataSource = null;
            dataGridView1.DataSource = sensitiveWords;
        }

        private void LoadSensitiveWords()
        {
            sensitiveWords = new List<SensitiveWord>();

            try
            {
                if (!File.Exists(configFilePath))
                {
                    // 如果配置文件不存在，创建默认配置
                    CreateDefaultConfig();
                }

                var doc = XDocument.Load(configFilePath);
                foreach (var wordElement in doc.Descendants("Word"))
                {
                    var word = new SensitiveWord
                    {
                        Text = wordElement.Element("Text")?.Value ?? "",
                        Category = wordElement.Element("Category")?.Value ?? "其他",
                        Description = wordElement.Element("Description")?.Value ?? "",
                        Enabled = bool.Parse(wordElement.Element("Enabled")?.Value ?? "true")
                    };
                    sensitiveWords.Add(word);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载敏感词配置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                CreateDefaultConfig();
            }
        }

        private void InitializeComponent()
        {
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.btnAdd = new System.Windows.Forms.Button();
            this.btnDelete = new System.Windows.Forms.Button();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.txtNewWord = new System.Windows.Forms.TextBox();
            this.cmbCategory = new System.Windows.Forms.ComboBox();
            this.txtDescription = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Location = new System.Drawing.Point(12, 12);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.Size = new System.Drawing.Size(760, 300);
            this.dataGridView1.TabIndex = 0;
            // 
            // btnAdd
            // 
            this.btnAdd.Location = new System.Drawing.Point(12, 380);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(75, 23);
            this.btnAdd.TabIndex = 1;
            this.btnAdd.Text = "添加";
            this.btnAdd.UseVisualStyleBackColor = true;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // btnDelete
            // 
            this.btnDelete.Location = new System.Drawing.Point(93, 380);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(75, 23);
            this.btnDelete.TabIndex = 2;
            this.btnDelete.Text = "删除";
            this.btnDelete.UseVisualStyleBackColor = true;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // btnSave
            // 
            this.btnSave.Location = new System.Drawing.Point(616, 380);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 3;
            this.btnSave.Text = "保存";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(697, 380);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // txtNewWord
            // 
            this.txtNewWord.Location = new System.Drawing.Point(80, 330);
            this.txtNewWord.Name = "txtNewWord";
            this.txtNewWord.Size = new System.Drawing.Size(150, 20);
            this.txtNewWord.TabIndex = 5;
            // 
            // cmbCategory
            // 
            this.cmbCategory.FormattingEnabled = true;
            this.cmbCategory.Items.AddRange(new object[] {
            "客户信息",
            "隐私信息",
            "金融信息",
            "系统标识",
            "其他"});
            this.cmbCategory.Location = new System.Drawing.Point(290, 330);
            this.cmbCategory.Name = "cmbCategory";
            this.cmbCategory.Size = new System.Drawing.Size(120, 21);
            this.cmbCategory.TabIndex = 6;
            // 
            // txtDescription
            // 
            this.txtDescription.Location = new System.Drawing.Point(480, 330);
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Size = new System.Drawing.Size(200, 20);
            this.txtDescription.TabIndex = 7;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 333);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 13);
            this.label1.TabIndex = 8;
            this.label1.Text = "敏感词：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(240, 333);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 13);
            this.label2.TabIndex = 9;
            this.label2.Text = "分类：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(420, 333);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 13);
            this.label3.TabIndex = 10;
            this.label3.Text = "描述：";
            // 
            // SensitiveWordsConfigForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 415);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.txtDescription);
            this.Controls.Add(this.cmbCategory);
            this.Controls.Add(this.txtNewWord);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnAdd);
            this.Controls.Add(this.dataGridView1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SensitiveWordsConfigForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "敏感词配置";
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.TextBox txtNewWord;
        private System.Windows.Forms.ComboBox cmbCategory;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;

        private void CreateDefaultConfig()
        {
            sensitiveWords = new List<SensitiveWord>
            {
                new SensitiveWord { Text = "客户", Category = "客户信息", Description = "客户相关信息", Enabled = true },
                new SensitiveWord { Text = "用户", Category = "客户信息", Description = "用户相关信息", Enabled = true },
                new SensitiveWord { Text = "个人信息", Category = "隐私信息", Description = "个人隐私相关信息", Enabled = true },
                new SensitiveWord { Text = "名单", Category = "客户信息", Description = "名单相关信息", Enabled = true },
                new SensitiveWord { Text = "消费者", Category = "客户信息", Description = "消费者相关信息", Enabled = true },
                new SensitiveWord { Text = "贷款", Category = "金融信息", Description = "贷款相关信息", Enabled = true },
                new SensitiveWord { Text = "账户", Category = "金融信息", Description = "账户相关信息", Enabled = true },
                new SensitiveWord { Text = "放款", Category = "金融信息", Description = "放款相关信息", Enabled = true },
                new SensitiveWord { Text = "交易", Category = "金融信息", Description = "交易相关信息", Enabled = true },
                new SensitiveWord { Text = "邮盾", Category = "系统标识", Description = "邮盾系统标识", Enabled = true }
            };
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtNewWord.Text))
            {
                MessageBox.Show("请输入敏感词", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (sensitiveWords.Any(w => w.Text.Equals(txtNewWord.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("该敏感词已存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var newWord = new SensitiveWord
            {
                Text = txtNewWord.Text.Trim(),
                Category = cmbCategory.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                Enabled = true
            };

            sensitiveWords.Add(newWord);
            RefreshDataGridView();

            // 清空输入框
            txtNewWord.Clear();
            txtDescription.Clear();
            cmbCategory.SelectedIndex = -1;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dataGridView1.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择要删除的敏感词", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("确定要删除选中的敏感词吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                var selectedWord = dataGridView1.SelectedRows[0].DataBoundItem as SensitiveWord;
                if (selectedWord != null)
                {
                    sensitiveWords.Remove(selectedWord);
                    RefreshDataGridView();
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SaveSensitiveWords();
                MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void SaveSensitiveWords()
        {
            var doc = new XDocument(
                new XElement("SensitiveWords",
                    sensitiveWords.Select(word =>
                        new XElement("Word",
                            new XElement("Text", word.Text),
                            new XElement("Category", word.Category),
                            new XElement("Description", word.Description),
                            new XElement("Enabled", word.Enabled)
                        )
                    )
                )
            );

            // 确保目录存在
            Directory.CreateDirectory(Path.GetDirectoryName(configFilePath));
            doc.Save(configFilePath);
        }
    }

    public class SensitiveWord
    {
        public string Text { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public bool Enabled { get; set; }
    }
}
