﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <asmv1:assemblyIdentity name="PostShield.dll" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" type="win32" />
  <description xmlns="urn:schemas-microsoft-com:asm.v1">PostShield</description>
  <application />
  <entryPoint>
    <co.v1:customHostSpecified />
  </entryPoint>
  <trustInfo>
    <security>
      <applicationRequestMinimum>
        <PermissionSet Unrestricted="true" ID="Custom" SameSite="site" />
        <defaultAssemblyRequest permissionSetReference="Custom" />
      </applicationRequestMinimum>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <!--
          UAC 清单选项
          如果要更改 Windows 用户帐户控制级别，请用以下节点之一替换
          requestedExecutionLevel 节点。

        <requestedExecutionLevel  level="asInvoker" uiAccess="false" />
        <requestedExecutionLevel  level="requireAdministrator" uiAccess="false" />
        <requestedExecutionLevel  level="highestAvailable" uiAccess="false" />

          如果要利用文件和注册表虚拟化提供
          向后兼容性，请删除 requestedExecutionLevel 节点。
    -->
        <requestedExecutionLevel level="asInvoker" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>
  <dependency>
    <dependentOS>
      <osVersionInfo>
        <os majorVersion="5" minorVersion="1" buildNumber="2600" servicePackMajor="0" />
      </osVersionInfo>
    </dependentOS>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Windows.CommonLanguageRuntime" version="4.0.30319.0" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Accessibility" version="4.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.CSharp" version="4.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Office.Tools" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Office.Tools.Common" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Office.Tools.Excel" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Office.Tools.v4.0.Framework" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.VisualStudio.Tools.Applications.Runtime" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="mscorlib" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="stdole" version="7.0.3300.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Core" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data.DataSetExtensions" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Drawing" version="4.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Management" version="4.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Windows.Forms" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.Linq" version="4.0.0.0" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Office.Tools.Common.v4.0.Utilities.dll" size="32664">
      <assemblyIdentity name="Microsoft.Office.Tools.Common.v4.0.Utilities" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>jLCTF8Mm6bD4PDN+rnzN6q0+ReXaNgPh68kMWgatFwI=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="PostShield.dll" size="100352">
      <assemblyIdentity name="PostShield" version="*******" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>1KruK4u/yawpNmzsIxcfNXjzuRJUE+B9cqAbCIU8qI8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <file name="Config\DataMaskingRules.xml" size="1046" writeableType="applicationData">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>YFfHvLOrMUrAZWSfQ3bjFG4M9azeUcnBj1GrbFG72Bk=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Config\Password.xml" size="100" writeableType="applicationData">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>cq39NRMv+C8CbqPdawVApHZjahjWbZ1NqnGtl8dtu58=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Everything\Everything.exe" size="2265096">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>Nc7+S8Spitc92kRExwCqyfdJ796PneamQ6V6W2Bb1Oc=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Everything\Everything32.dll" size="88072">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>O6ZNCO2/reyOMBZz34s2+fdHXINYeTD8lXfqNm7AaDk=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Everything\Everything64.dll" size="97800">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>4syNeoJiqLpHYYJV4oNq+VzL/NKIw23uDPOgCTtWet8=</dsig:DigestValue>
    </hash>
  </file>
  <file name="PostShield.dll.config" size="77">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>sRXPO8NcIiuVI4ajMrs7gnxty0j6ChK7OorE6bM0xfQ=</dsig:DigestValue>
    </hash>
  </file>
  <vstav3:addIn xmlns:vstav3="urn:schemas-microsoft-com:vsta.v3">
    <vstav3:entryPointsCollection>
      <vstav3:entryPoints>
        <vstav3:entryPoint class="PostShield.ThisAddIn">
          <assemblyIdentity name="PostShield" version="*******" language="neutral" processorArchitecture="msil" />
        </vstav3:entryPoint>
      </vstav3:entryPoints>
    </vstav3:entryPointsCollection>
    <vstav3:update enabled="true">
      <vstav3:expiration maximumAge="7" unit="days" />
    </vstav3:update>
    <vstav3:application>
      <vstov4:customizations xmlns:vstov4="urn:schemas-microsoft-com:vsto.v4">
        <vstov4:customization>
          <vstov4:appAddIn application="Excel" loadBehavior="3" keyName="PostShield">
            <vstov4:friendlyName>PostShield</vstov4:friendlyName>
            <vstov4:description>PostShield</vstov4:description>
            <vstov4.1:ribbonTypes xmlns:vstov4.1="urn:schemas-microsoft-com:vsto.v4.1">
              <vstov4.1:ribbonType name="PostShield.Ribbon1, PostShield, Version=*******, Culture=neutral, PublicKeyToken=null" />
            </vstov4.1:ribbonTypes>
          </vstov4:appAddIn>
        </vstov4:customization>
      </vstov4:customizations>
    </vstav3:application>
  </vstav3:addIn>
<publisherIdentity name="CN=admin\Administrator" issuerKeyHash="0a5dfe65ddc627c647db835da15dd8fc41ca3534" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>qwyMbpbQQpPIAAV8734hosc7qjJjXQTDc5chKq7S7/w=</DigestValue></Reference></SignedInfo><SignatureValue>mu+VOOAgxFbkLR1VAfK2eoVKzRGvbNwuAfHrp/p3bstyqkMR8h6jWVAand+fuUYUFod1/c0WD1t3Qc4YAW/pJMmD/xwPEVMjO4JXnWVwEe0AXdJCKb7OU30JDCqfQEuxtrzMVha+JVRx3TBeBzMEbhPi0TmPsFf6SycIRnJqqic=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>zgl4fm2isrHb2uVdUDCZACLMmPCnIB06xA6U7WILKp4BFMmBT9NPE6TP9l2JFgw90WF1X5XveOiHAspf0XlW+GITGE4xG9qSbqP+o/sCkmg7z1jdI3z658cmC1YT3a1wDc8nY4t6+ewSXnaSA84kWj3gpUV6tYpxvJexWY5xorU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="fcefd2ae2a219773c3045d6332aa3bc7a2217eef7c0500c89342d0966e8c0cab" Description="" Url=""><as:assemblyIdentity name="PostShield.dll" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" type="win32" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=admin\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>ibtz+ID8oQfOgp0GBiDifnyN0usDmwmthPVqGeUQRfM=</DigestValue></Reference></SignedInfo><SignatureValue>xHDpeZwLcAY87tTpM5IG4g6jU5H2hzNPD2dJASy2u3eENk/vP+tVC5GeiEs4Kp0VHs3V+6ajSO7q9kMwN+IuvsieMRsg9KppmUUKxvERShLWI0r+04IdQLqXcPqfxk1dfBPGhhv6kkLCXlCOYDUCaewPc6JNy15Xf/EV+ZLA9NQ=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>zgl4fm2isrHb2uVdUDCZACLMmPCnIB06xA6U7WILKp4BFMmBT9NPE6TP9l2JFgw90WF1X5XveOiHAspf0XlW+GITGE4xG9qSbqP+o/sCkmg7z1jdI3z658cmC1YT3a1wDc8nY4t6+ewSXnaSA84kWj3gpUV6tYpxvJexWY5xorU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB5TCCAU6gAwIBAgIQPYNc4p43tp9DH8VpyRLsjTANBgkqhkiG9w0BAQsFADAxMS8wLQYDVQQDHiYAYQBkAG0AaQBuAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjAeFw0yNDExMDYwMzA1MjZaFw0yNTExMDYwOTA1MjZaMDExLzAtBgNVBAMeJgBhAGQAbQBpAG4AXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDOCXh+baKysdva5V1QMJkAIsyY8KcgHTrEDpTtYgsqngEUyYFP008TpM/2XYkWDD3RYXVfle946IcCyl/ReVb4YhMYTjEb2pJuo/6j+wKSaDvPWN0jfPrnxyYLVhPdrXANzydji3r57BJedpIDziRaPeClRXq1inG8l7FZjnGitQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBAK0PA6KSxjKsUAOv01iZ9DXwkYlOZSCeUdAVQehSYPyMxhS9v9DqQEiKqyiuknimYPdG9vIWoJRbfnodcljStARR+KP3/WsEx1czwhkZD0sVaoOIMSYdYP0c1QLykZlEqUdTHNC2XSUqH/osyc77Xb4lJ6chl1803qlVoVtj3Xe9</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>