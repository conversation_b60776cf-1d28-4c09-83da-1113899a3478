{"Version": 1, "WorkspaceRootPath": "F:\\实习\\PostShieldV1.8\\PostShield\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|f:\\实习\\postshieldv1.8\\postshield\\ribbon1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|solutionrelative:ribbon1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|F:\\实习\\PostShieldV1.8\\PostShield\\properties\\settings.settings||{6D2695F9-5365-4A78-89ED-F205C045BFE6}", "RelativeMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|solutionrelative:properties\\settings.settings||{6D2695F9-5365-4A78-89ED-F205C045BFE6}"}, {"AbsoluteMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|F:\\实习\\PostShieldV1.8\\PostShield\\ThisAddIn.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|solutionrelative:ThisAddIn.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|F:\\实习\\PostShieldV1.8\\PostShield\\thisaddin.designer.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|solutionrelative:thisaddin.designer.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|solutionrelative:Ribbon1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.Designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E9247FC-F9F9-403B-83D5-17778911AA2F}|PostShield.csproj|solutionrelative:Ribbon1.Designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e5c86464-96be-4d7c-9a8b-abcb3bbf5f92}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Ribbon1.cs", "DocumentMoniker": "F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.cs", "RelativeDocumentMoniker": "Ribbon1.cs", "ToolTip": "F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.cs", "RelativeToolTip": "Ribbon1.cs", "ViewState": "AgIAAA4GAAAAAAAAAAA1wEgGAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T03:11:11.61Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Settings.settings", "DocumentMoniker": "F:\\实习\\PostShieldV1.8\\PostShield\\Properties\\Settings.settings", "RelativeDocumentMoniker": "Properties\\Settings.settings", "ToolTip": "F:\\实习\\PostShieldV1.8\\PostShield\\Properties\\Settings.settings", "RelativeToolTip": "Properties\\Settings.settings", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002767|", "WhenOpened": "2025-07-02T06:51:52.918Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ThisAddIn.Designer.xml", "DocumentMoniker": "F:\\实习\\PostShieldV1.8\\PostShield\\ThisAddIn.Designer.xml", "RelativeDocumentMoniker": "ThisAddIn.Designer.xml", "ToolTip": "F:\\实习\\PostShieldV1.8\\PostShield\\ThisAddIn.Designer.xml", "RelativeToolTip": "ThisAddIn.Designer.xml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-07-02T03:16:17.726Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ThisAddIn.cs", "DocumentMoniker": "F:\\实习\\PostShieldV1.8\\PostShield\\ThisAddIn.cs", "RelativeDocumentMoniker": "ThisAddIn.cs", "ToolTip": "F:\\实习\\PostShieldV1.8\\PostShield\\ThisAddIn.cs", "RelativeToolTip": "ThisAddIn.cs", "ViewState": "AgIAAA0AAAAAAAAAAIBAwBQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T03:11:58.053Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "Ribbon1.cs [设计]", "DocumentMoniker": "F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.cs", "RelativeDocumentMoniker": "Ribbon1.cs", "ToolTip": "F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.cs [设计]", "RelativeToolTip": "Ribbon1.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-08T04:44:37.495Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Ribbon1.Designer.cs", "DocumentMoniker": "F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.Designer.cs", "RelativeDocumentMoniker": "Ribbon1.Designer.cs", "ToolTip": "F:\\实习\\PostShieldV1.8\\PostShield\\Ribbon1.Designer.cs", "RelativeToolTip": "Ribbon1.Designer.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAuwB8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T02:56:11.518Z"}]}]}]}