﻿using System;
using System.IO;
using System.Windows.Forms;

public class EnvironmentChecker
{
    // 目标目录名称（由构造函数传入）
    private readonly string _directoryName;

    // 需要检查的文件列表
    private static readonly string[] RequiredFiles = { "Everything.exe", "Everything64.dll" };

    // 静态字段，用于存储文件路径
    public static string ExeFilePath { get; private set; }
    public static string DllFilePath { get; private set; }

    /// <summary>
    /// 构造函数，初始化目标目录名称
    /// </summary>
    /// <param name="directoryName">要检查的目录名称。</param>
    public EnvironmentChecker(string directoryName)
    {
        _directoryName = directoryName;
    }

    /// <summary>
    /// 检查环境是否有效，返回布尔值并输出缺失的文件列表。
    /// </summary>
    /// <param name="missingFiles">缺失的文件列表。</param>
    /// <returns>如果所有文件存在则返回 true，否则返回 false。</returns>
    public bool IsEnvironmentValid(out string[] missingFiles)
    {
        string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
        string targetDirectory = Path.Combine(baseDirectory, _directoryName);

        // 设置静态字段路径
        ExeFilePath = Path.Combine(targetDirectory, "Everything.exe");
        DllFilePath = Path.Combine(targetDirectory, "Everything64.dll");

        // 检查目标目录是否存在
        if (!Directory.Exists(targetDirectory))
        {
            try
            {
                Directory.CreateDirectory(targetDirectory);
                MessageBox.Show($"{_directoryName} 目录不存在，已创建新目录。", "目录创建", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法创建目录 {_directoryName}。错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                missingFiles = RequiredFiles; // 如果目录创建失败，则认为所有文件缺失
                return false;
            }
        }

        // 获取缺失的文件
        missingFiles = GetMissingFiles(targetDirectory);
        return missingFiles.Length == 0;
    }

    /// <summary>
    /// 获取缺失的文件列表。
    /// </summary>
    /// <param name="directory">目标目录路径。</param>
    /// <returns>缺失的文件名称数组。</returns>
    private string[] GetMissingFiles(string directory)
    {
        var missingFiles = new System.Collections.Generic.List<string>();

        foreach (var file in RequiredFiles)
        {
            string filePath = Path.Combine(directory, file);
            if (!File.Exists(filePath))
            {
                missingFiles.Add(file);
            }
        }

        return missingFiles.ToArray();
    }
}
