﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using static EverythingSearchApp.EverythingSearchAddin;

namespace PostShield
{
    // 文件信息的结构
    internal class MyFileInfo
    {
        public string FileName { get; set; }
        public string Path { get; set; }
        public string Size { get; set; }
        public string DateCreated { get; set; }
        public string DateModified { get; set; }
        public string DateAccessed { get; set; }
    }

    internal class EverythingHelper
    {
        // 启动 everything.exe
        public static bool StartEverythingIfNotRunning()
        {
            // 环境检查
            var exeFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Everything", "Everything.exe");
            var dllFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Everything", "Everything64.dll");

            if (!(File.Exists(exeFilePath) && File.Exists(dllFilePath)))
            {
                // 弹出提示框
                MessageBox.Show($"Everything文件缺失", "文件检查", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 检查 Everything 是否已在运行
            Process[] processes = Process.GetProcessesByName("Everything");
            if (processes.Length == 0)
            {
                try
                {
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        FileName = exeFilePath,
                        WindowStyle = ProcessWindowStyle.Hidden,
                        CreateNoWindow = true,
                        Verb = "runas"  // 使用管理员权限启动
                    };
                    Process.Start(startInfo);

                    // 增加延迟等待 Everything 完全启动
                    System.Threading.Thread.Sleep(2000);  // 等待 2 秒

                    //
                    return true;
                }
                catch (Exception ex)
                {
                    //
                    MessageBox.Show("无法启动 Everything 程序，请检查路径是否正确。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    return false;
                }
            }
            else
            {
                //
                return true;
            }
        }

        public static bool ExecuteSearchQuery(string searchQuery)
        {
            if (string.IsNullOrEmpty(searchQuery))
            {
                MessageBox.Show("未输入搜索内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            Everything_SetSearchW(searchQuery);

            Everything_SetRequestFlags(
                EVERYTHING_REQUEST_FILE_NAME |
                EVERYTHING_REQUEST_PATH |
                EVERYTHING_REQUEST_SIZE |
                EVERYTHING_REQUEST_DATE_MODIFIED |
                EVERYTHING_REQUEST_DATE_CREATED |
                EVERYTHING_REQUEST_DATE_ACCESSED
            );

            bool querySuccess = Everything_QueryW(true);
            if (!querySuccess)
            {
                uint error = Everything_GetLastError();
                return false;
            }

            return true;
        }

        // 读取文件信息
        public static List<MyFileInfo> ReadFileInfo()
        {
            int results = Everything_GetNumResults();

            List<MyFileInfo> fileInfoList = new List<MyFileInfo>();

            if (results > 0)
            {
                for (int i = 0; i < results; i++)
                {
                    try
                    {
                        // 获取目录路径（指向内部结构的指针）
                        IntPtr pathPtr = Everything_GetResultPath(i);

                        // 检查指针是否为 NULL
                        string path = pathPtr != IntPtr.Zero ? Marshal.PtrToStringUni(pathPtr) : "未知路径";

                        // 获取文件名
                        IntPtr fileNamePtr = Everything_GetResultFileNameW(i);
                        string fileName = fileNamePtr != IntPtr.Zero ? Marshal.PtrToStringUni(fileNamePtr) : "未知文件";

                        // 将目录路径和文件名组合成完整路径
                        string fullPath = Path.Combine(path, fileName);

                        // 获取文件大小
                        LARGE_INTEGER fileSizeStruct = new LARGE_INTEGER();
                        string fileSizeDisplay = "";
                        try
                        {
                            bool sizeAvailable = Everything_GetResultSize(i, ref fileSizeStruct);
                            if (sizeAvailable)
                            {
                                long fileSize = fileSizeStruct.ToInt64() / 1024;
                                fileSizeDisplay = fileSize > 0 ? fileSize.ToString("N0", CultureInfo.InvariantCulture) + " KB" : "";
                            }
                        }
                        catch (Exception ex)
                        {
                            // 捕获异常
                        }

                        // 获取文件时间信息
                        System.Runtime.InteropServices.ComTypes.FILETIME fileTime;
                        string dateCreatedString = Everything_GetResultDateCreated(i, out fileTime) != 0
                            ? GetDateTimeString(fileTime)
                            : "无创建时间";
                        string dateModifiedString = Everything_GetResultDateModified(i, out fileTime) != 0
                            ? GetDateTimeString(fileTime)
                            : "无修改时间";
                        string dateAccessedString = Everything_GetResultDateAccessed(i, out fileTime) != 0
                            ? GetDateTimeString(fileTime)
                            : "无访问时间";

                        // 将文件信息存入列表
                        fileInfoList.Add(new MyFileInfo
                        {
                            FileName = fileName,
                            Path = path,  
                            Size = fileSizeDisplay,
                            DateCreated = dateCreatedString,
                            DateModified = dateModifiedString,
                            DateAccessed = dateAccessedString
                        });
                    }
                    catch (Exception ex)
                    {
                        // 捕获并处理异常
                    }
                }
            }
            return fileInfoList;
        }

        #region 帮助方法和文件信息结构
        // 帮助方法：将FILETIME转换为日期字符串
        private static string GetDateTimeString(System.Runtime.InteropServices.ComTypes.FILETIME fileTime)
        {
            return DateTime.FromFileTime((((long)fileTime.dwHighDateTime) << 32) | (uint)fileTime.dwLowDateTime).ToString("yyyy/M/d H:mm");
        }

        #endregion
    }
}
