﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="PostShield.vsto" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="PostShield" asmv2:product="PostShield" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="PostShield.dll.manifest" size="15954">
      <assemblyIdentity name="PostShield.dll" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>D8iH1ZH/NTfVn0RAyeM5tNFe2rgwds2PhQxtzlPwX9s=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=admin\Administrator" issuerKeyHash="0a5dfe65ddc627c647db835da15dd8fc41ca3534" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>50uSKQC2z4IglNcTrHuq3VBMf3Aa9HcXPsbgous5GrI=</DigestValue></Reference></SignedInfo><SignatureValue>NGX+BqDC4D3INg9JoGzGtGpeSxmdtWVnNCrmIZwA81rEtHH2Bq8Taqj64jAMrkQ3Z9xVyfR/F3woAoEDL918KLqj/KYyig//T/aXaapwQtROGsYDK28gkaMV/+Vh3v0QX3LoBYNHV3vN5NtRnXgVgoaVqFgCboeXltqKhwwEspw=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>zgl4fm2isrHb2uVdUDCZACLMmPCnIB06xA6U7WILKp4BFMmBT9NPE6TP9l2JFgw90WF1X5XveOiHAspf0XlW+GITGE4xG9qSbqP+o/sCkmg7z1jdI3z658cmC1YT3a1wDc8nY4t6+ewSXnaSA84kWj3gpUV6tYpxvJexWY5xorU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="b21a39eba2e0c63e1777f41a707f4c50ddaa7bac13d7942082cfb60029924be7" Description="" Url=""><as:assemblyIdentity name="PostShield.vsto" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=admin\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>F/bdDgX7G6qz1gqyWMaWzMd+HxxlIN2ofdYXTcCwARk=</DigestValue></Reference></SignedInfo><SignatureValue>GYvX+6GVL4vHGL6fjVPsQzc31WzyY3Vr3qVa8rvUoy+fVyhv/kpNiaq5/hHVLV5uUMRegHvreIDOTU2jhjsbGMWkE0GkAJL9z2pt/h1X2d+w8pRouQBkAI5X+VGIO/ifWvrLf1krIHDeILpWoPna6n2NU598lPDD7Ugws+k7Daw=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>zgl4fm2isrHb2uVdUDCZACLMmPCnIB06xA6U7WILKp4BFMmBT9NPE6TP9l2JFgw90WF1X5XveOiHAspf0XlW+GITGE4xG9qSbqP+o/sCkmg7z1jdI3z658cmC1YT3a1wDc8nY4t6+ewSXnaSA84kWj3gpUV6tYpxvJexWY5xorU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB5TCCAU6gAwIBAgIQPYNc4p43tp9DH8VpyRLsjTANBgkqhkiG9w0BAQsFADAxMS8wLQYDVQQDHiYAYQBkAG0AaQBuAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjAeFw0yNDExMDYwMzA1MjZaFw0yNTExMDYwOTA1MjZaMDExLzAtBgNVBAMeJgBhAGQAbQBpAG4AXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDOCXh+baKysdva5V1QMJkAIsyY8KcgHTrEDpTtYgsqngEUyYFP008TpM/2XYkWDD3RYXVfle946IcCyl/ReVb4YhMYTjEb2pJuo/6j+wKSaDvPWN0jfPrnxyYLVhPdrXANzydji3r57BJedpIDziRaPeClRXq1inG8l7FZjnGitQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBAK0PA6KSxjKsUAOv01iZ9DXwkYlOZSCeUdAVQehSYPyMxhS9v9DqQEiKqyiuknimYPdG9vIWoJRbfnodcljStARR+KP3/WsEx1czwhkZD0sVaoOIMSYdYP0c1QLykZlEqUdTHNC2XSUqH/osyc77Xb4lJ6chl1803qlVoVtj3Xe9</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>