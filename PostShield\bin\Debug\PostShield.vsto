﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="PostShield.vsto" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="PostShield" asmv2:product="PostShield" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="PostShield.dll.manifest" size="15955">
      <assemblyIdentity name="PostShield.dll" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>PrtrYGsMQJeTVuzCSG0aZRgamOOrEjz6JeHSNV8JWVI=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=admin\Administrator" issuerKeyHash="0a5dfe65ddc627c647db835da15dd8fc41ca3534" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>TnZj+9cP/0D8M0ARTBiqhKUaSHfFOEnlTwOiVHcdk9E=</DigestValue></Reference></SignedInfo><SignatureValue>bRpnC6ahZHIDv4WWNG9cFEF2Uc6qVmhJkXB8LRReAYkOoYASc4Mo9sokf6B37PyhOpi2fzGe94HuwEU91zmDYNM7C5r9of4yK+yfKJW+wbu/tIMZ4DY72e9se0p9LJ8FwpW1WMLUhjWYeZqRkX7Gok6lbMQi+LSQQMegAW37JRM=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>zgl4fm2isrHb2uVdUDCZACLMmPCnIB06xA6U7WILKp4BFMmBT9NPE6TP9l2JFgw90WF1X5XveOiHAspf0XlW+GITGE4xG9qSbqP+o/sCkmg7z1jdI3z658cmC1YT3a1wDc8nY4t6+ewSXnaSA84kWj3gpUV6tYpxvJexWY5xorU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="d1931d7754a2034fe54938c577481aa584aa184c114033fc40ff0fd7fb63764e" Description="" Url=""><as:assemblyIdentity name="PostShield.vsto" version="*******" publicKeyToken="8de97921280693a0" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=admin\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>vr65/M/LhQEmvO8SPte2e69EwQcFHNeiuYed+0XGOXE=</DigestValue></Reference></SignedInfo><SignatureValue>dJfbJH+x22THcLx690WnV+40UkQwJmwQDK/rYeIHbfK3wAO8YYCVEDMERaiZWHN3il85QN8LS9S5qZXPA6jBviv+Z55RGJtGk7SlYDw7nTHIg6BYDFFh5g7x1ZnJsg1kOdp4hdfXdjqWxsmtHhr8tAH3fOYPCMLBxYoYYmg5GJ8=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>zgl4fm2isrHb2uVdUDCZACLMmPCnIB06xA6U7WILKp4BFMmBT9NPE6TP9l2JFgw90WF1X5XveOiHAspf0XlW+GITGE4xG9qSbqP+o/sCkmg7z1jdI3z658cmC1YT3a1wDc8nY4t6+ewSXnaSA84kWj3gpUV6tYpxvJexWY5xorU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB5TCCAU6gAwIBAgIQPYNc4p43tp9DH8VpyRLsjTANBgkqhkiG9w0BAQsFADAxMS8wLQYDVQQDHiYAYQBkAG0AaQBuAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjAeFw0yNDExMDYwMzA1MjZaFw0yNTExMDYwOTA1MjZaMDExLzAtBgNVBAMeJgBhAGQAbQBpAG4AXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDOCXh+baKysdva5V1QMJkAIsyY8KcgHTrEDpTtYgsqngEUyYFP008TpM/2XYkWDD3RYXVfle946IcCyl/ReVb4YhMYTjEb2pJuo/6j+wKSaDvPWN0jfPrnxyYLVhPdrXANzydji3r57BJedpIDziRaPeClRXq1inG8l7FZjnGitQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBAK0PA6KSxjKsUAOv01iZ9DXwkYlOZSCeUdAVQehSYPyMxhS9v9DqQEiKqyiuknimYPdG9vIWoJRbfnodcljStARR+KP3/WsEx1czwhkZD0sVaoOIMSYdYP0c1QLykZlEqUdTHNC2XSUqH/osyc77Xb4lJ6chl1803qlVoVtj3Xe9</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>