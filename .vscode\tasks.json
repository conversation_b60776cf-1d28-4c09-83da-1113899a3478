{"version": "2.0.0", "tasks": [{"type": "amigaassembly", "vasm": {"enabled": true, "command": "${config:amiga-assembly.binDir}/vasmm68k_mot", "args": ["-m68000", "-<PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON>"], "leaveWarnings": true}, "vlink": {"enabled": true, "command": "${config:amiga-assembly.binDir}/vlink", "includes": "*.{s,S,asm,ASM}", "excludes": "", "createStartupSequence": true, "createExeFileParentDir": true, "exefilename": "../uae/dh0/myprogram", "args": ["-b<PERSON><PERSON><PERSON><PERSON>", "-Bstatic"]}, "problemMatcher": [], "label": "amigaassembly: build"}]}