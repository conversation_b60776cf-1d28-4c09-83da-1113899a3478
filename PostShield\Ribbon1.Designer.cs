﻿using Microsoft.Office.Tools.Ribbon;
using System;

namespace PostShield
{
    partial class Ribbon1 : Microsoft.Office.Tools.Ribbon.RibbonBase
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        public Ribbon1()
            : base(Globals.Factory.GetRibbonFactory())
        {
            InitializeComponent();
        }

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Ribbon1));
            this.tab1 = this.Factory.CreateRibbonTab();
            this.grpSearch = this.Factory.CreateRibbonGroup();
            this.btnSearchALL = this.Factory.CreateRibbonButton();
            this.btnFileCheck = this.Factory.CreateRibbonButton();
            this.btnCopyFile = this.Factory.CreateRibbonButton();
            this.btnAnalyzeExcelFile = this.Factory.CreateRibbonButton();
            this.group1 = this.Factory.CreateRibbonGroup();
            this.btnDataMasking = this.Factory.CreateRibbonButton();
            this.group2 = this.Factory.CreateRibbonGroup();
            this.btnDataMaskingOK = this.Factory.CreateRibbonButton();
            this.btnSave = this.Factory.CreateRibbonButton();
            this.group3 = this.Factory.CreateRibbonGroup();
            this.btnSearchFile = this.Factory.CreateRibbonButton();
            this.btnSearchSysInfo = this.Factory.CreateRibbonButton();
            this.btnLocateFile = this.Factory.CreateRibbonButton();
            this.butDeleteCacheWX = this.Factory.CreateRibbonButton();
            this.btnDeleteCacheQQ = this.Factory.CreateRibbonButton();
            this.group4 = this.Factory.CreateRibbonGroup();
            this.btnMenu = this.Factory.CreateRibbonButton();
            this.btnConfig = this.Factory.CreateRibbonButton();
            this.btnSensitiveWordsConfig = this.Factory.CreateRibbonButton();
            this.tab1.SuspendLayout();
            this.grpSearch.SuspendLayout();
            this.group1.SuspendLayout();
            this.group2.SuspendLayout();
            this.group3.SuspendLayout();
            this.group4.SuspendLayout();
            this.SuspendLayout();
            // 
            // tab1
            // 
            this.tab1.Groups.Add(this.grpSearch);
            this.tab1.Groups.Add(this.group1);
            this.tab1.Groups.Add(this.group2);
            this.tab1.Groups.Add(this.group3);
            this.tab1.Groups.Add(this.group4);
            this.tab1.Label = "邮盾V1.8";
            this.tab1.Name = "tab1";
            // 
            // grpSearch
            // 
            this.grpSearch.Items.Add(this.btnSearchALL);
            this.grpSearch.Items.Add(this.btnFileCheck);
            this.grpSearch.Items.Add(this.btnCopyFile);
            this.grpSearch.Items.Add(this.btnAnalyzeExcelFile);
            this.grpSearch.Label = "文件检查";
            this.grpSearch.Name = "grpSearch";
            // 
            // btnSearchALL
            // 
            this.btnSearchALL.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnSearchALL.Image = ((System.Drawing.Image)(resources.GetObject("btnSearchALL.Image")));
            this.btnSearchALL.Label = "全盘搜索";
            this.btnSearchALL.Name = "btnSearchALL";
            this.btnSearchALL.OfficeImageId = "FindDialog";
            this.btnSearchALL.ShowImage = true;
            this.btnSearchALL.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnSearchALL_Click);
            // 
            // btnFileCheck
            // 
            this.btnFileCheck.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnFileCheck.Label = "疑似检查";
            this.btnFileCheck.Name = "btnFileCheck";
            this.btnFileCheck.OfficeImageId = "ReviewShowMarkupMenu";
            this.btnFileCheck.ShowImage = true;
            this.btnFileCheck.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnFileCheck_Click);
            // 
            // btnCopyFile
            // 
            this.btnCopyFile.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnCopyFile.Label = "处理文件";
            this.btnCopyFile.Name = "btnCopyFile";
            this.btnCopyFile.OfficeImageId = "DataValidation";
            this.btnCopyFile.ShowImage = true;
            this.btnCopyFile.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnCopyFile_Click);
            // 
            // btnAnalyzeExcelFile
            // 
            this.btnAnalyzeExcelFile.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnAnalyzeExcelFile.Label = "文件透视";
            this.btnAnalyzeExcelFile.Name = "btnAnalyzeExcelFile";
            this.btnAnalyzeExcelFile.OfficeImageId = "ReadingMode";
            this.btnAnalyzeExcelFile.ShowImage = true;
            this.btnAnalyzeExcelFile.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnAnalyzeExcelFiles_Click);
            // 
            // group1
            // 
            this.group1.Items.Add(this.btnDataMasking);
            this.group1.Label = "数据脱敏";
            this.group1.Name = "group1";
            // 
            // btnDataMasking
            // 
            this.btnDataMasking.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnDataMasking.Label = "字段脱敏";
            this.btnDataMasking.Name = "btnDataMasking";
            this.btnDataMasking.OfficeImageId = "EditBusinessCard";
            this.btnDataMasking.ShowImage = true;
            this.btnDataMasking.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnDataMasking_Click);
            // 
            // group2
            // 
            this.group2.Items.Add(this.btnDataMaskingOK);
            this.group2.Items.Add(this.btnSave);
            this.group2.Label = "脱敏后操作";
            this.group2.Name = "group2";
            // 
            // btnDataMaskingOK
            // 
            this.btnDataMaskingOK.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnDataMaskingOK.Label = "脱敏确认";
            this.btnDataMaskingOK.Name = "btnDataMaskingOK";
            this.btnDataMaskingOK.OfficeImageId = "FilePermissionView";
            this.btnDataMaskingOK.ShowImage = true;
            this.btnDataMaskingOK.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnDataMaskingOK_Click);
            // 
            // btnSave
            // 
            this.btnSave.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnSave.Label = "保存检查";
            this.btnSave.Name = "btnSave";
            this.btnSave.OfficeImageId = "FileSave";
            this.btnSave.ShowImage = true;
            this.btnSave.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnSave_Click);
            // 
            // group3
            // 
            this.group3.Items.Add(this.btnSearchFile);
            this.group3.Items.Add(this.btnSearchSysInfo);
            this.group3.Items.Add(this.btnLocateFile);
            this.group3.Items.Add(this.butDeleteCacheWX);
            this.group3.Items.Add(this.btnDeleteCacheQQ);
            this.group3.Label = "工具";
            this.group3.Name = "group3";
            // 
            // btnSearchFile
            // 
            this.btnSearchFile.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnSearchFile.Label = "专项搜索";
            this.btnSearchFile.Name = "btnSearchFile";
            this.btnSearchFile.OfficeImageId = "SourceControlShowDifferences";
            this.btnSearchFile.ShowImage = true;
            this.btnSearchFile.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnSearchFile_Click);
            // 
            // btnSearchSysInfo
            // 
            this.btnSearchSysInfo.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnSearchSysInfo.Label = "系统信息";
            this.btnSearchSysInfo.Name = "btnSearchSysInfo";
            this.btnSearchSysInfo.OfficeImageId = "_3DMaterialMetal";
            this.btnSearchSysInfo.ShowImage = true;
            this.btnSearchSysInfo.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnSearchSysInfo_Click);
            // 
            // btnLocateFile
            // 
            this.btnLocateFile.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnLocateFile.Label = "定位文件";
            this.btnLocateFile.Name = "btnLocateFile";
            this.btnLocateFile.OfficeImageId = "WindowSwitchWindowsMenuExcel";
            this.btnLocateFile.ShowImage = true;
            this.btnLocateFile.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnLocateFile_Click);
            // 
            // butDeleteCacheWX
            // 
            this.butDeleteCacheWX.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.butDeleteCacheWX.Label = "微信清理";
            this.butDeleteCacheWX.Name = "butDeleteCacheWX";
            this.butDeleteCacheWX.OfficeImageId = "BuildingBlocksCreateTableOfAuthorities";
            this.butDeleteCacheWX.ShowImage = true;
            this.butDeleteCacheWX.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnDeleteCacheWX_Click);
            // 
            // btnDeleteCacheQQ
            // 
            this.btnDeleteCacheQQ.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.btnDeleteCacheQQ.Label = "QQ清理";
            this.btnDeleteCacheQQ.Name = "btnDeleteCacheQQ";
            this.btnDeleteCacheQQ.OfficeImageId = "BuildingBlocksCreateTableOfAuthorities";
            this.btnDeleteCacheQQ.ShowImage = true;
            this.btnDeleteCacheQQ.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnDeleteCacheQQ_Click);
            // 
            // group4
            // 
            this.group4.Items.Add(this.btnMenu);
            this.group4.Items.Add(this.btnConfig);
            this.group4.Items.Add(this.btnSensitiveWordsConfig);
            this.group4.Label = "帮助";
            this.group4.Name = "group4";
            // 
            // btnMenu
            // 
            this.btnMenu.Label = "使用说明";
            this.btnMenu.Name = "btnMenu";
            this.btnMenu.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnMenu_Click);
            // 
            // btnConfig
            //
            this.btnConfig.Label = "配置";
            this.btnConfig.Name = "btnConfig";
            this.btnConfig.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnConfig_Click);
            //
            // btnSensitiveWordsConfig
            //
            this.btnSensitiveWordsConfig.Label = "敏感词配置";
            this.btnSensitiveWordsConfig.Name = "btnSensitiveWordsConfig";
            this.btnSensitiveWordsConfig.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.btnSensitiveWordsConfig_Click);
            //
            // Ribbon1
            // 
            this.Name = "Ribbon1";
            this.RibbonType = "Microsoft.Excel.Workbook";
            this.Tabs.Add(this.tab1);
            this.Load += new Microsoft.Office.Tools.Ribbon.RibbonUIEventHandler(this.Ribbon1_Load);
            this.tab1.ResumeLayout(false);
            this.tab1.PerformLayout();
            this.grpSearch.ResumeLayout(false);
            this.grpSearch.PerformLayout();
            this.group1.ResumeLayout(false);
            this.group1.PerformLayout();
            this.group2.ResumeLayout(false);
            this.group2.PerformLayout();
            this.group3.ResumeLayout(false);
            this.group3.PerformLayout();
            this.group4.ResumeLayout(false);
            this.group4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        internal Microsoft.Office.Tools.Ribbon.RibbonTab tab1;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup grpSearch;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btnSearchALL;
        internal RibbonButton btnFileCheck;
        internal RibbonButton btnLocateFile;
        internal RibbonGroup group1;

        internal RibbonButton btnCopyFile;
        internal RibbonButton btnDataMasking;
        internal RibbonButton btnAnalyzeExcelFile;
        internal RibbonGroup group3;
        internal RibbonButton btnSearchFile;
        internal RibbonButton btnSearchSysInfo;
        internal RibbonButton btnDataMaskingOK;
        internal RibbonGroup group2;
        internal RibbonButton btnSave;
        internal RibbonGroup group4;
        internal RibbonButton btnConfig;
        internal RibbonButton btnMenu;
        internal RibbonButton btnSensitiveWordsConfig;
        internal RibbonButton butDeleteCacheWX;
        internal RibbonButton btnDeleteCacheQQ;
    }

    partial class ThisRibbonCollection
    {
        internal Ribbon1 Ribbon1
        {
            get { return this.GetRibbon<Ribbon1>(); }
        }
    }
}
