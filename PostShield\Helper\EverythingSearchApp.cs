﻿using System;
using System.IO;
using System.Runtime.InteropServices;

namespace EverythingSearchApp
{
    public class EverythingSearchAddin
    {
        // SDK imports
        private const string dllPath = @"Everything\Everything64.dll";


        [DllImport(dllPath, CharSet = CharSet.Unicode)]
        public static extern void Everything_SetSearchW(string search);

        [DllImport(dllPath)]
        public static extern void Everything_SetRequestFlags(uint flags);

        [DllImport(dllPath)]
        public static extern bool Everything_QueryW(bool wait);

        [DllImport(dllPath)]
        public static extern int Everything_GetNumResults();

        [DllImport(dllPath, CharSet = CharSet.Unicode)]
        public static extern void Everything_GetResultFullPathNameW(int index, string buffer, int bufferSize);

        [DllImport(dllPath, CharSet = CharSet.Unicode)]
        public static extern IntPtr Everything_GetResultPath(int index);

        [DllImport(dllPath, CharSet = CharSet.Unicode)]
        public static extern IntPtr Everything_GetResultFileNameW(int index);

        [DllImport(dllPath)]
        public static extern int Everything_GetResultDateCreated(int index, out System.Runtime.InteropServices.ComTypes.FILETIME dateCreated);

        [DllImport(dllPath)]
        public static extern int Everything_GetResultDateModified(int index, out System.Runtime.InteropServices.ComTypes.FILETIME dateModified);

        [DllImport(dllPath)]
        public static extern int Everything_GetResultDateAccessed(int index, out System.Runtime.InteropServices.ComTypes.FILETIME dateAccessed);

        [DllImport(dllPath)]
        public static extern bool Everything_GetResultSize(int index, ref LARGE_INTEGER lpSize);

        [DllImport(dllPath)]
        public static extern uint Everything_GetLastError();

        public const uint EVERYTHING_REQUEST_FILE_NAME = 0x00000001;
        public const uint EVERYTHING_REQUEST_PATH = 0x00000002;
        public const uint EVERYTHING_REQUEST_SIZE = 0x00000010;
        public const uint EVERYTHING_REQUEST_DATE_MODIFIED = 0x00000020;
        public const uint EVERYTHING_REQUEST_DATE_CREATED = 0x00000040;
        public const uint EVERYTHING_REQUEST_DATE_ACCESSED = 0x00000080;

        // 定义 LARGE_INTEGER 结构体
        [StructLayout(LayoutKind.Sequential)]
        public struct LARGE_INTEGER
        {
            public int LowPart;
            public int HighPart;

            public long ToInt64()
            {
                return ((long)HighPart << 32) | (uint)LowPart;
            }
        }
    }
}
