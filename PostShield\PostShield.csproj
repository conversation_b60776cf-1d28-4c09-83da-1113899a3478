﻿<Project ToolsVersion="17.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <!--
    This section defines project-level properties.

    AssemblyName
      Name of the output assembly.
    Configuration
      Specifies a default value for debug.
    OutputType
      Must be "Library" for VSTO.
    Platform
      Specifies what CPU the output of this project can run on.
    NoStandardLibraries
      Set to "false" for VSTO.
    RootNamespace
      In C#, this specifies the namespace given to new files. In VB, all objects are
      wrapped in this namespace at runtime.
  -->
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8E9247FC-F9F9-403B-83D5-17778911AA2F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>PostShield</RootNamespace>
    <AssemblyName>PostShield</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <DefineConstants>VSTO40;UseOfficeInterop</DefineConstants>
    <ResolveComReferenceSilent>true</ResolveComReferenceSilent>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <PublishUrl>E:\Internship\</PublishUrl>
    <InstallUrl />
    <TargetCulture>zh-chs</TargetCulture>
    <ApplicationVersion>*******</ApplicationVersion>
    <AutoIncrementApplicationRevision>true</AutoIncrementApplicationRevision>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>days</UpdateIntervalUnits>
    <ProductName>PostShield</ProductName>
    <PublisherName />
    <SupportUrl />
    <FriendlyName>PostShield</FriendlyName>
    <OfficeApplicationDescription />
    <LoadBehavior>3</LoadBehavior>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <!--
      OfficeApplication
        Add-in host application
    -->
    <OfficeApplication>Excel</OfficeApplication>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Debug" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Release" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section specifies references for the project.
  -->
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Office.Interop.Excel.15.0.4795.1001\lib\net20\Microsoft.Office.Interop.Excel.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Excel, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="$([System.String]::Copy(&quot;;$(DefineConstants);&quot;).ToLower().Contains(';useofficeinterop;')) or $([System.String]::Copy(&quot;,$(DefineConstants),&quot;).ToLower().Contains(',useofficeinterop,'))">
      <ItemGroup>
        <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
      </ItemGroup>
    </When>
    <Otherwise>
      <ItemGroup>
        <COMReference Include="Microsoft.Office.Core">
          <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>7</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Office.Interop.Excel">
          <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
          <VersionMajor>1</VersionMajor>
          <VersionMinor>8</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
      </ItemGroup>
    </Otherwise>
  </Choose>
  <ItemGroup>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <!--
    This section defines the user source files that are part of the project.
     
    A "Compile" element specifies a source file to compile.
    An "EmbeddedResource" element specifies an .resx file for embedded resources.
    A "None" element specifies a file that is not to be passed to the compiler (for instance, 
    a text file or XML file).
    The "AppDesigner" element specifies the directory where the application properties files
    can be found.
  -->
  <ItemGroup>
    <Compile Include="Helper\EnvironmentChecker.cs" />
    <Compile Include="Helper\EverythingHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Helper\EverythingSearchApp.cs" />
    <EmbeddedResource Include="Resource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Ribbon1.resx">
      <DependentUpon>Ribbon1.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="PostShield_TemporaryKey.pfx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Resource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource.resx</DependentUpon>
    </Compile>
    <Compile Include="Ribbon1.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Ribbon1.Designer.cs">
      <DependentUpon>Ribbon1.cs</DependentUpon>
    </Compile>
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Config\DataMaskingRules.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Config\Password.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Everything\Everything.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Everything\Everything32.dll" />
    <Content Include="Everything\Everything64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\icons8-微信-48.jpg" />
    <None Include="Resources\icons8-qq-48.jpg" />
    <None Include="Resources\27520_AutoSaveSwitch_32.jpg" />
    <None Include="Resources\13033_FileMarkAsFinal_32.jpg" />
    <None Include="Resources\12672_WindowSwitchWindowsMenuExcel_32.jpg" />
    <None Include="Resources\12387_TableStyleClear_32.jpg" />
    <None Include="Resources\2042_ReviewHighlightChanges_32.jpg" />
    <None Include="Resources\2034_DataValidation_32.jpg" />
    <None Include="Resources\1849_FindDialogExcel_32.jpg" />
    <None Include="Resources\1561_ViewVisualBasicCode_32.jpg" />
    <None Include="Resources\860_DataFormExcel_32.jpg" />
    <None Include="Resources\109_FilePrintPreview_32.jpg" />
    <None Include="Resources\icons8-qq-48.png" />
    <None Include="Resources\icons8-微信-48.png" />
    <None Include="Resources\12672_WindowSwitchWindowsMenuExcel_32.png" />
    <None Include="Resources\1561_ViewVisualBasicCode_32.png" />
    <None Include="Resources\109_FilePrintPreview_32.png" />
    <None Include="Resources\13033_FileMarkAsFinal_32.png" />
    <None Include="Resources\12387_TableStyleClear_32.png" />
    <None Include="Resources\1964_ClearAll_32.png" />
    <None Include="Resources\860_DataFormExcel_32.png" />
    <None Include="Resources\2034_DataValidation_32.png" />
    <None Include="Resources\2042_ReviewHighlightChanges_32.png" />
    <None Include="Resources\1849_FindDialogExcel_32.png" />
    <None Include="Resources\27520_AutoSaveSwitch_322.png" />
    <None Include="Resources\27520_AutoSaveSwitch_321.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>PostShield_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>F01E8EF4CC7368097A8D035DFC3325EE4DBC3DC5</ManifestCertificateThumbprint>
  </PropertyGroup>
  <!-- Include the build rules for a C# project. -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Include additional build rules for an Office application add-in. -->
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- This section defines VSTO properties that describe the host-changeable project properties. -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="Excel" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="Excel" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\Excel\InstallRoot\Path#excel.exe" DebugInfoCommandLine="/x" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="Excel" GeneratedCodeNamespace="PostShield" PublishedHash="69C324AB27932AA2FBF2B7EA72250886FF164DE6" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" PublishedHash="5FA42E590C396AE2DABA93C641FD973EF1E930B8" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>