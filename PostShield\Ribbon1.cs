using Microsoft.Office.Tools.Ribbon;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using Excel = Microsoft.Office.Interop.Excel;
using System.Management;
using System.Net.NetworkInformation;
using System.Net;
using Microsoft.Win32;
using Microsoft.Office.Interop.Excel;



namespace PostShield
{
    public partial class Ribbon1
    {
        private static readonly TraceSource traceSource = new TraceSource("PostShieldTraceSource");
        private bool isStatusBarMessageShown = false; // 添加一个标志位
        private System.Windows.Forms.Timer statusBarTimer; // 添加一个计时器

        private void Ribbon1_Load(object sender, RibbonUIEventArgs e)
        {
            // 设置 TraceSource 配置
            var logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "log.txt");
            Directory.CreateDirectory(Path.GetDirectoryName(logFilePath));
            traceSource.Listeners.Add(new TextWriterTraceListener(logFilePath));
            traceSource.Switch = new SourceSwitch("PostShieldSwitch", "All");

            // 订阅 SheetBeforeDoubleClick 事件
            Globals.ThisAddIn.Application.SheetBeforeDoubleClick += Workbook_SheetBeforeDoubleClick;

            // 初始化计时器
            statusBarTimer = new System.Windows.Forms.Timer();
            statusBarTimer.Tick += StatusBarTimer_Tick;
            statusBarTimer.Interval = 3000; // 5秒
        }


        #region 双击打开
        private void Workbook_SheetBeforeDoubleClick(object Sh, Range Target, ref bool Cancel)
        {
            // 1. 判断当前工作表名称是否为“疑似敏感”或“搜索结果”或“邮盾文件”
            Excel.Worksheet worksheet = Sh as Excel.Worksheet;
            Excel.Application app = worksheet.Application;

            // 清空状态栏信息
            app.StatusBar = false;


            // 检查当前工作表名称是否为“搜索结果”或“疑似敏感”
            bool isTargetSheet = worksheet.Name == "搜索结果" || worksheet.Name == "疑似敏感" || worksheet.Name == "邮盾文件" || worksheet.Name == "专项搜索" || worksheet.Name == "文件透视";
            if (!isTargetSheet)
            {
                if (!isStatusBarMessageShown) // 检查是否已经显示过提示信息
                {
                    // 在状态栏显示提示信息
                    app.StatusBar = "此工作表不支持双击打开文件的功能。请在 '搜索结果'、'疑似敏感'、'邮盾文件' 、'专项搜索' 或 '文件透视' 工作表中使用此功能。";
                    isStatusBarMessageShown = true; // 设置标志位为 true

                    // 启动计时器以清除状态栏信息
                    statusBarTimer.Start();
                }
                return;
            }



            // 2. 检查是否点击了 A 列或 B 列
            if (Target.Column != 1 && Target.Column != 2)
            {
                app.StatusBar = "此操作仅适用于点击文件名或文件路径。";
                return;
            }

            // 3. 获取目录路径和文件名
            string directoryPath = worksheet.Cells[Target.Row, 2]?.Value2?.ToString(); // B 列为路径
            string fileName = worksheet.Cells[Target.Row, 1]?.Value2?.ToString();      // A 列为文件名

            if (string.IsNullOrWhiteSpace(directoryPath))
            {
                app.StatusBar = "路径为空，无法执行此操作。";
                return;
            }

            // 4. 根据点击的列执行操作
            if (Target.Column == 1) // 点击 A 列（文件名）时，打开文件
            {
                if (string.IsNullOrWhiteSpace(fileName))
                {
                    MessageBox.Show("文件名为空，无法执行此操作。");
                    return;
                }

                string fullPath = Path.Combine(directoryPath, fileName);

                // 检查文件是否存在
                if (!File.Exists(fullPath))
                {
                    MessageBox.Show("路径不是有效的文件路径。");
                    return;
                }

                // 显示自定义的自动关闭的消息窗口
                ShowAutoClosingMessageBox("正在打开文件...", "请稍候", 1000); // 显示 1 秒后自动关闭

                // 在后台打开文件
                Task.Run(() =>
                {
                    try
                    {
                        System.Diagnostics.Process.Start(fullPath);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件无法打开：{ex.Message}");
                    }
                });
            }
            else if (Target.Column == 2) // 点击 B 列（文件路径）时，打开文件夹
            {
                // 检查目录是否存在
                string fullPath = Path.Combine(directoryPath, fileName);
                if (!File.Exists(fullPath))
                {
                    MessageBox.Show("文件路径无效，无法定位到文件。");
                    return;
                }

                // 显示自定义的自动关闭的消息窗口
                ShowAutoClosingMessageBox("正在定位文件...", "请稍候", 1000); // 显示 1 秒后自动关闭

                // 在后台打开文件夹并定位到文件
                Task.Run(() =>
                {
                    try
                    {
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{fullPath}\"");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件夹无法打开：{ex.Message}");
                    }
                });
            }


        }

        private void StatusBarTimer_Tick(object sender, EventArgs e)
        {
            // 停止计时器
            statusBarTimer.Stop();

            // 清除状态栏信息
            Globals.ThisAddIn.Application.StatusBar = false;
        }
        private void ShowAutoClosingMessageBox(string message, string title, int timeout)
        {
            // 创建一个自定义窗口（Form），用于显示消息
            Form messageForm = new Form
            {
                Size = new Size(250, 100),
                Text = title,
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                ControlBox = false
            };

            // 创建一个标签来显示消息内容
            var label = new System.Windows.Forms.Label
            {
                Text = message,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };
            messageForm.Controls.Add(label);

            // 使用 Timer 设置窗口的自动关闭
            var timer = new System.Windows.Forms.Timer { Interval = timeout };
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                messageForm.Close();
            };
            timer.Start();

            // 显示窗口并阻止用户操作，直到窗口关闭
            messageForm.ShowDialog();
        }
        #endregion

        #region 搜索

        private void btnSearchALL_Click(object sender, RibbonControlEventArgs e)
        {
            List<MyFileInfo> fileInfoList = null;
            string searchQuery = @"*.xlsx | *.xls | *.et";

            // 调用检查并启动 Everything 的方法
            if (!EverythingHelper.StartEverythingIfNotRunning()) return;

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            // 定义消息窗口、计时器和超时标志
            Form messageForm = null;
            System.Windows.Forms.Timer closeTimer = null;
            System.Windows.Forms.Timer timeoutTimer = null;
            bool searchCompleted = false;


            try
            {
                // 显示自动关闭的消息窗口，提示用户正在执行搜索操作
                var result = ShowAutoClosingMessageBoxSearch("正在查询文件，请稍候...", "查询中", 5000); // 设定最大显示时间5秒
                messageForm = result.Item1;
                closeTimer = result.Item2;

                // 设置30秒超时计时器
                timeoutTimer = new System.Windows.Forms.Timer { Interval = 5000 }; // 30秒
                timeoutTimer.Tick += (s, args) =>
                {
                    // 停止超时计时器并关闭消息窗口
                    timeoutTimer.Stop();
                    closeTimer?.Stop();
                    messageForm?.Close();

                    // 显示超时错误提示
                    MessageBox.Show("查询超时，请点击帮助按钮排查。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // 设置搜索未完成标志
                    searchCompleted = false;
                };
                timeoutTimer.Start();

                // 启动
                var isOk = EverythingHelper.ExecuteSearchQuery(searchQuery);
                if (!isOk)
                {
                    MessageBox.Show("查询失败，请确保 Everything 已在后台运行。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 读取信息
                fileInfoList = EverythingHelper.ReadFileInfo();
                searchCompleted = true; // 标记为查询成功完成
            }
            catch (Exception ex)
            {
                traceSource.TraceEvent(TraceEventType.Error, 0, $"异常发生: {ex.Message}");
                traceSource.Flush();
                MessageBox.Show($"发生异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 停止所有计时器并关闭消息窗口
                closeTimer?.Stop();
                timeoutTimer?.Stop();
                messageForm?.Close();
            }

            // 在 finally 块之后检查是否成功完成搜索
            if (!searchCompleted)
            {
                // 如果搜索未完成（超时或错误），尝试关闭 Everything 进程
                foreach (var process in Process.GetProcessesByName("Everything"))
                {
                    try
                    {
                        process.Kill(); // 关闭进程
                        process.WaitForExit(); // 等待进程退出
                    }
                    catch (Exception ex)
                    {
                        // 记录或处理关闭进程时的异常
                        Console.WriteLine($"关闭 Everything 进程失败: {ex.Message}，请手动打开资源管理器关闭");
                    }
                }

                // 返回以退出当前方法
                return;
            }

            stopwatch.Stop();
            double elapsedSeconds = stopwatch.Elapsed.TotalSeconds;

            var app = Globals.ThisAddIn.Application;

            // 创建或获取表
            Excel.Worksheet worksheet = app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().Any(sheet => sheet.Name == "搜索结果")
                ? app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().First(sheet => sheet.Name == "搜索结果")
                : (Excel.Worksheet)app.ActiveWorkbook.Worksheets.Add();
            worksheet.Name = "搜索结果";
            worksheet.Cells.Clear();
            object[,] data = new object[,]
            {
                { "文件名", "路径", "文件大小 (KB)", "创建时间", "修改时间", "访问时间" }
            };
            Excel.Range writeRange = worksheet.Range[worksheet.Cells[1, 1], worksheet.Cells[1, data.GetLength(1)]];
            writeRange.Value = data;

            WriteToExcel(worksheet, fileInfoList);

            MessageBox.Show($"搜索 {fileInfoList.Count} 个文件，用时 {elapsedSeconds:F2} 秒。", "搜索结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            traceSource.TraceEvent(TraceEventType.Information, 0, $"搜索完成并输出到工作表。用时 {elapsedSeconds:F2} 秒。");
            traceSource.Flush();
        }


        // 修改后的 ShowAutoClosingMessageBox 方法，返回 Form 和 Timer 元组
        private Tuple<Form, System.Windows.Forms.Timer> ShowAutoClosingMessageBoxSearch(string message, string title, int timeout)
        {
            // 创建一个自定义窗口（Form），用于显示消息
            Form messageForm = new Form
            {
                Size = new Size(250, 100),
                Text = title,
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                ControlBox = false
            };
            // 创建一个标签来显示消息内容
            var label = new System.Windows.Forms.Label
            {
                Text = message,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };
            messageForm.Controls.Add(label);

            // 使用 Timer 设置窗口的自动关闭
            var timer = new System.Windows.Forms.Timer { Interval = timeout };
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                messageForm.Close();
            };
            timer.Start();

            // 显示窗口并返回，以便后续控制
            messageForm.Show();
            return Tuple.Create(messageForm, timer);
        }


        private void WriteToExcel(Excel.Worksheet worksheet, List<MyFileInfo> fileInfoList)
        {
            try
            {
                // 获取 MyFileInfo 的字段信息列表
                var fields = typeof(MyFileInfo).GetProperties(BindingFlags.Public | BindingFlags.Instance);

                // 创建一个二维数组用于存储数据
                object[,] data = new object[fileInfoList.Count, fields.Length];

                // 遍历 fileInfoList 和字段信息，将数据动态填充到数组中
                for (int i = 0; i < fileInfoList.Count; i++)
                {
                    var info = fileInfoList[i];
                    for (int j = 0; j < fields.Length; j++)
                    {
                        data[i, j] = fields[j].GetValue(info);
                    }
                }

                Excel.Range startCell = worksheet.Cells[2, 1];
                Excel.Range endCell = worksheet.Cells[fileInfoList.Count + 1, fields.Length];
                Excel.Range writeRange = worksheet.Range[startCell, endCell];
                writeRange.Value = data;

                Excel.Range columnC = worksheet.Range["C2", $"C{fileInfoList.Count + 1}"];
                columnC.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;

                worksheet.Columns["A"].AutoFit();
                worksheet.Columns["C:F"].AutoFit();

                traceSource.TraceEvent(TraceEventType.Information, 0, $"{fileInfoList.Count} 个条目已成功写入 Excel。");
                traceSource.Flush();
            }
            catch (Exception ex)
            {
                traceSource.TraceEvent(TraceEventType.Error, 0, $"写入 Excel 时发生异常：{ex.Message}");
                traceSource.Flush();
                MessageBox.Show($"写入 Excel 时发生异常：\n错误信息：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 专项搜索

        private void btnSearchFile_Click(object sender, RibbonControlEventArgs e)
        {
            List<MyFileInfo> fileInfoList = null;

            // 调用检查并启动 Everything 的方法
            if (!EverythingHelper.StartEverythingIfNotRunning()) return;

            // 显示输入框让用户输入搜索关键字
            string searchQuery = ShowInputDialog("请输入要搜索的内容（例如 *.pdf 或 *合同*）：");
            if (string.IsNullOrEmpty(searchQuery))
            {
                MessageBox.Show("未输入搜索内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            // 定义消息窗口、计时器和超时标志
            Form messageForm = null;
            System.Windows.Forms.Timer closeTimer = null;
            System.Windows.Forms.Timer timeoutTimer = null;
            bool searchCompleted = false;

            try
            {
                // 显示自动关闭的消息窗口，提示用户正在执行搜索操作
                var result = ShowAutoClosingMessageBoxSearch("正在查询文件，请稍候...", "查询中", 5000); // 设定最大显示时间5秒
                messageForm = result.Item1;
                closeTimer = result.Item2;

                // 设置30秒超时计时器
                timeoutTimer = new System.Windows.Forms.Timer { Interval = 30000 }; // 30秒
                timeoutTimer.Tick += (s, args) =>
                {
                    // 停止超时计时器并关闭消息窗口
                    timeoutTimer.Stop();
                    closeTimer?.Stop();
                    messageForm?.Close();

                    // 显示超时错误提示
                    MessageBox.Show("查询超时，请点击帮助按钮排查。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // 设置搜索未完成标志
                    searchCompleted = false;
                };
                timeoutTimer.Start();

                // 执行查询
                var isOk = EverythingHelper.ExecuteSearchQuery(searchQuery);
                if (!isOk)
                {
                    MessageBox.Show("查询失败，请确保 Everything 已在后台运行。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 读取查询信息
                fileInfoList = EverythingHelper.ReadFileInfo();
                searchCompleted = true; // 标记为查询成功完成
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            finally
            {
                // 停止所有计时器并关闭消息窗口
                closeTimer?.Stop();
                timeoutTimer?.Stop();
                messageForm?.Close();
            }

            // 在 finally 块之后检查是否成功完成搜索
            if (!searchCompleted)
            {
                // 如果搜索未完成（超时或错误），尝试关闭 Everything 进程
                foreach (var process in Process.GetProcessesByName("Everything"))
                {
                    try
                    {
                        process.Kill(); // 关闭进程
                        process.WaitForExit(); // 等待进程退出
                    }
                    catch (Exception ex)
                    {
                        // 记录或处理关闭进程时的异常
                        Console.WriteLine($"关闭 Everything 进程失败: {ex.Message}，请手动打开资源管理器关闭");
                    }
                }

                // 返回以退出当前方法
                return;
            }

            stopwatch.Stop();
            double elapsedSeconds = stopwatch.Elapsed.TotalSeconds;

            var app = Globals.ThisAddIn.Application;
            Excel.Worksheet worksheet = GetOrCreateWorksheet("专项搜索");
            worksheet.Cells.Clear();

            // 写入表头
            object[,] data = new object[,]
            {
        { "文件名", "路径", "文件大小 (KB)", "创建时间", "修改时间", "访问时间" }
            };
            Excel.Range writeRange = worksheet.Range[worksheet.Cells[1, 1], worksheet.Cells[1, data.GetLength(1)]];
            writeRange.Value = data;

            // 调用现有的 WriteToExcel 方法来写入数据
            WriteToExcel(worksheet, fileInfoList);

            MessageBox.Show($"搜索 {fileInfoList.Count} 个文件，用时 {elapsedSeconds:F2} 秒。", "专项搜索", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // ShowInputDialog方法用于显示输入框供用户输入搜索关键字
        private static string ShowInputDialog(string text)
        {
            Form prompt = new Form()
            {
                Width = 400,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = "输入搜索关键字",
                StartPosition = FormStartPosition.CenterScreen
            };
            System.Windows.Forms.Label textLabel = new System.Windows.Forms.Label() { Left = 20, Top = 20, Text = text, Width = 350 };
            System.Windows.Forms.TextBox textBox = new System.Windows.Forms.TextBox() { Left = 20, Top = 50, Width = 350 };
            System.Windows.Forms.Button confirmation = new System.Windows.Forms.Button() { Text = "确定", Left = 150, Width = 100, Top = 80, DialogResult = DialogResult.None };

            // 添加确认按钮点击事件，验证输入
            confirmation.Click += (sender, e) =>
            {
                if (string.IsNullOrWhiteSpace(textBox.Text) || textBox.Text.Trim() == "*")
                {
                    MessageBox.Show("请输入有效的搜索关键字，不能只包含'*'。", "输入无效", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    prompt.DialogResult = DialogResult.OK;
                    prompt.Close();
                }
            };

            prompt.Controls.Add(textLabel);
            prompt.Controls.Add(textBox);
            prompt.Controls.Add(confirmation);
            prompt.AcceptButton = confirmation;

            return prompt.ShowDialog() == DialogResult.OK ? textBox.Text : string.Empty;
        }


        // GetOrCreateWorksheet方法用于获取或创建一个名为“搜索结果”的工作表
        private Excel.Worksheet GetOrCreateWorksheet(string sheetName)
        {
            var app = Globals.ThisAddIn.Application;

            foreach (Excel.Worksheet sheet in app.ActiveWorkbook.Worksheets)
            {
                if (sheet.Name == sheetName)
                {
                    return sheet;
                }
            }

            Excel.Worksheet newSheet = app.ActiveWorkbook.Worksheets.Add();
            newSheet.Name = sheetName;
            return newSheet;
        }

        #endregion

        #region 检查

        private void btnFileCheck_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            var worksheetOfAll = app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().FirstOrDefault(sheet => sheet.Name == "搜索结果");
            if (worksheetOfAll == null)
            {
                MessageBox.Show("请先搜索文件");
                return;
            }

            if (worksheetOfAll.Cells[1, 1].Value?.ToString() != "文件名")
            {
                MessageBox.Show("第1单元格内容不是 '文件名'，请检查表格格式。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            Excel.Worksheet worksheetOfCheck = app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().FirstOrDefault(sheet => sheet.Name == "疑似敏感")
                ?? (Excel.Worksheet)app.ActiveWorkbook.Worksheets.Add();
            worksheetOfCheck.Name = "疑似敏感";
            worksheetOfCheck.Cells.Clear();
            object[,] headerData = new object[,]
            {
        { "文件名", "路径", "文件大小 (KB)", "创建时间", "修改时间", "访问时间", "配匹关键词" }
            };
            Excel.Range headerRange = worksheetOfCheck.Range[worksheetOfCheck.Cells[1, 1], worksheetOfCheck.Cells[1, headerData.GetLength(1)]];
            headerRange.Value = headerData;

            // 用于存储包含“邮盾”关键词的文件行
            List<object[]> rowsToExport = new List<object[]>();

            string sensitiveWords;
            int lastColumnCheck = worksheetOfCheck.UsedRange.Columns.Count + 1;

            int lastRow = worksheetOfAll.UsedRange.Rows.Count;
            object[,] allData = worksheetOfAll.Range[worksheetOfAll.Cells[2, 1], worksheetOfAll.Cells[lastRow, worksheetOfAll.UsedRange.Columns.Count]].Value;
            List<object[]> rowsToCopy = new List<object[]>();

            for (int row = 1; row <= allData.GetLength(0); row++)
            {
                string fileName = allData[row, 1]?.ToString();

                if (!string.IsNullOrEmpty(fileName) && ContainsSensitiveKeywords(fileName, out sensitiveWords))
                {
                    object[] rowData = new object[allData.GetLength(1) + 1];
                    for (int col = 0; col < allData.GetLength(1); col++)
                    {
                        rowData[col] = allData[row, col + 1];
                    }
                    rowData[rowData.Length - 1] = sensitiveWords;

                    // 判断是否包含“邮盾”关键词
                    if (fileName.Contains("邮盾"))
                    {
                        rowsToExport.Add(rowData);  // 添加到导出列表
                    }
                    else
                    {
                        rowsToCopy.Add(rowData);  // 添加到“疑似敏感”表格
                        Excel.Range currentRowRange = worksheetOfAll.Rows[row + 1];
                        currentRowRange.Interior.Color = Excel.XlRgbColor.rgbRed;
                    }
                }
            }

            // 将“疑似敏感”的行写入现有工作表
            if (rowsToCopy.Count > 0)
            {
                object[,] dataToWrite = new object[rowsToCopy.Count, rowsToCopy[0].Length];
                for (int i = 0; i < rowsToCopy.Count; i++)
                {
                    for (int j = 0; j < rowsToCopy[i].Length; j++)
                    {
                        dataToWrite[i, j] = rowsToCopy[i][j];
                    }
                }
                int startRow = 2;
                Excel.Range writeRange = worksheetOfCheck.Range[worksheetOfCheck.Cells[startRow, 1], worksheetOfCheck.Cells[startRow + rowsToCopy.Count - 1, dataToWrite.GetLength(1)]];
                writeRange.Value = dataToWrite;
                Excel.Range columnC = worksheetOfCheck.Range["C2", $"C{rowsToCopy.Count + 1}"];
                Excel.Range columnG = worksheetOfCheck.Range["G2", $"G{rowsToCopy.Count + 1}"];
                columnC.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                columnG.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                worksheetOfCheck.Columns["A"].AutoFit();
                worksheetOfCheck.Columns["C:G"].AutoFit();
            }

            // 将包含“邮盾”的行写入同一工作簿中的新工作表
            Excel.Worksheet worksheetOfExport = app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().FirstOrDefault(sheet => sheet.Name == "邮盾文件")
                ?? (Excel.Worksheet)app.ActiveWorkbook.Worksheets.Add();
            worksheetOfExport.Name = "邮盾文件";
            worksheetOfExport.Cells.Clear();

            // 写入标题行
            Excel.Range exportHeaderRange = worksheetOfExport.Range[worksheetOfExport.Cells[1, 1], worksheetOfExport.Cells[1, headerData.GetLength(1)]];
            exportHeaderRange.Value = headerData;

            // 将包含“邮盾”的行写入新工作簿
            if (rowsToExport.Count > 0)
            {
                // 写入数据行
                object[,] exportData = new object[rowsToExport.Count, rowsToExport[0].Length];
                for (int i = 0; i < rowsToExport.Count; i++)
                {
                    for (int j = 0; j < rowsToExport[i].Length; j++)
                    {
                        exportData[i, j] = rowsToExport[i][j];
                    }
                }
                Excel.Range exportWriteRange = worksheetOfExport.Range[worksheetOfExport.Cells[2, 1], worksheetOfExport.Cells[1 + rowsToExport.Count, exportData.GetLength(1)]];
                exportWriteRange.Value = exportData;

                worksheetOfExport.Columns["A"].AutoFit();
                worksheetOfExport.Columns["C:G"].AutoFit();
            }
        }

        private bool ContainsSensitiveKeywords(string fileName, out string sensitiveWords)
        {
            List<string> sensitiveKeywords = new List<string>
    {
        "客户", "用户", "个人信息", "名单",
        "消费者", "贷款", "账户", "放款",
        "交易", "邮盾"  // 添加“邮盾”关键词
    };

            var matchedKeywords = sensitiveKeywords.Where(keyword => fileName.Contains(keyword)).ToList();
            sensitiveWords = string.Join(", ", matchedKeywords);
            return matchedKeywords.Any();
        }


        #endregion

        #region 定位

        private void btnLocateFile_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            Excel.Worksheet activeSheet = app.ActiveSheet;

            // 检查当前工作表名称是否为“搜索结果”或“疑似敏感”
            if (activeSheet.Name != "搜索结果" && activeSheet.Name != "疑似敏感" && activeSheet.Name != "邮盾文件" && activeSheet.Name != "专项搜索" && activeSheet.Name != "文件透视")
            {
                MessageBox.Show("请在 '搜索结果'、'疑似敏感' 、 '邮盾文件' 、 '专项搜索' 或 '文件透视'  工作表中使用此功能。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查第1行第2列的内容是否为“路径”
            if (activeSheet.Cells[1, 2].Value?.ToString() != "路径")
            {
                MessageBox.Show("第1行第2列的内容不是 '路径'，请检查表格格式。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 获取当前选中单元格的行号
            int selectedRow = app.ActiveCell.Row;

            // 如果是第一行或选中的行第2列为空，提示并返回
            if (selectedRow == 1 || activeSheet.Cells[selectedRow, 2].Value == null)
            {
                MessageBox.Show("请选中包含路径的行。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            // 获取当前行第2列（路径）和第3列（文件名）的内容，并组合成完整路径
            string directoryPath = activeSheet.Cells[selectedRow, 2].Value.ToString();
            string fileName = activeSheet.Cells[selectedRow, 1].Value.ToString();
            string fullPath = Path.Combine(directoryPath, fileName);

            // 检查完整路径是否有效
            if (!Path.IsPathRooted(fullPath) || !File.Exists(fullPath))
            {
                MessageBox.Show("无效的文件路径，无法定位。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 打开文件所在目录并选中该文件
            try
            {
                string argument = $"/select,\"{fullPath}\"";
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "explorer",
                    Arguments = argument,
                    UseShellExecute = true
                };

                // 添加 200 毫秒的延迟以确保资源管理器完全加载并选中文件
                Thread.Sleep(500);
                Process.Start(psi);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件所在目录：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 处理

        private string GetPasswordFromXml(string filePath)
        {
            try
            {
                XDocument doc = XDocument.Load(filePath);
                return doc.Root.Element("Password")?.Value;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法加载密码配置文件：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        private void btnCopyFile_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            Excel.Worksheet activeSheet = app.ActiveSheet;

            // 检查当前工作表名称是否为“搜索结果”或“疑似敏感”
            if (activeSheet.Name == "邮盾文件")
            {
                MessageBox.Show("该文件已被处理，无需再次处理", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            else if (activeSheet.Name != "搜索结果" && activeSheet.Name != "疑似敏感")
            {
                MessageBox.Show("请在 '搜索结果' 或 '疑似敏感' 工作表中使用此功能。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查第1行第2列的内容是否为“路径”
            if (activeSheet.Cells[1, 2].Value?.ToString() != "路径")
            {
                MessageBox.Show("第1行第2列的内容不是 '路径'，请检查表格格式。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 获取当前选中单元格的行号
            int selectedRow = app.ActiveCell.Row;

            // 如果是第一行或选中的行第2列为空，提示并返回
            if (selectedRow == 1 || activeSheet.Cells[selectedRow, 2].Value == null)
            {
                MessageBox.Show("请选中包含路径的行。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }


            // 获取当前行第2列（路径）和第1列（文件名）的内容，并组合成完整路径
            string directoryPath = activeSheet.Cells[selectedRow, 2].Value.ToString();
            string fileName = activeSheet.Cells[selectedRow, 1].Value.ToString();
            string fullPath = Path.Combine(directoryPath, fileName);

            // 检查完整路径是否有效
            if (!Path.IsPathRooted(fullPath) || !File.Exists(fullPath))
            {
                MessageBox.Show("无效的文件路径，无法定位。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }



            // 获取文件的目录、文件名和扩展名，设置当前日期
            string directory = Path.GetDirectoryName(fullPath);
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fullPath);
            string extension = Path.GetExtension(fullPath);
            string dateSuffix = DateTime.Today.ToString("yyyyMMdd"); // 当前日期

            // 创建加密文件的路径
            string encryptedFilePath = Path.Combine(directory, $"{fileNameWithoutExtension}_邮盾已加密_{dateSuffix}{extension}");

            // 创建 Excel 应用程序对象
            var excelApp = new Excel.Application();
            Excel.Workbook workbook = null;

            // 读取密码配置
            string configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "Password.xml");
            string password = GetPasswordFromXml(configFilePath);

            if (string.IsNullOrEmpty(password))
            {
                MessageBox.Show("未能加载加密密码，操作已取消。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                workbook = excelApp.Workbooks.Open(fullPath);
                workbook.Password = password;
                workbook.SaveAs(encryptedFilePath, Password: password);
                workbook.Password = null;

                string newOriginalFilePath = Path.Combine(directory, $"{fileNameWithoutExtension}_邮盾待脱敏_{dateSuffix}{extension}");
                File.Move(fullPath, newOriginalFilePath);

                MessageBox.Show($"文件加密成功！新文件路径：{encryptedFilePath}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "explorer",
                    Arguments = $"/select,\"{newOriginalFilePath}\"",
                    UseShellExecute = true
                };
                Process.Start(psi);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                workbook?.Close(false);
                excelApp.Quit();
            }
        }

        //检查后确认
        private void btnDataMaskingOK_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            Excel.Worksheet activeSheet = app.ActiveSheet;

            // 检查当前工作表名称是否为“邮盾文件”
            if (activeSheet.Name != "邮盾文件")
            {
                MessageBox.Show("请在 '邮盾文件' 工作表中使用此功能。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查第1行第2列的内容是否为“路径”
            if (activeSheet.Cells[1, 2].Value?.ToString() != "路径")
            {
                MessageBox.Show("第1行第2列的内容不是 '路径'，请检查表格格式。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 获取用户选择的范围
            Excel.Range selectedRange = app.Selection;

            foreach (Excel.Range row in selectedRange.Rows)
            {
                // 获取每一行的行号
                int rowIndex = row.Row;

                // 如果是第一行或路径为空，跳过当前行
                if (rowIndex == 1 || activeSheet.Cells[rowIndex, 2].Value == null)
                {
                    continue;
                }

                // 获取路径和文件名
                string directoryPath = activeSheet.Cells[rowIndex, 2].Value.ToString();
                string fileName = activeSheet.Cells[rowIndex, 1].Value.ToString();
                string fullPath = Path.Combine(directoryPath, fileName);

                // 检查完整路径是否有效
                if (!Path.IsPathRooted(fullPath) || !File.Exists(fullPath))
                {
                    MessageBox.Show($"无效的文件路径：{fullPath}，跳过此文件。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    continue;
                }

                // 检查文件名中是否包含 "_邮盾待脱敏_" 字样
                if (!fileName.Contains("_邮盾待脱敏_"))
                {
                    MessageBox.Show($"文件名中未包含 '_邮盾待脱敏_'：{fileName}，跳过此文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    continue;
                }

                try
                {
                    // 修改文件名，去掉 "_邮盾待脱敏_" 字样
                    // 修改文件名，将 "_邮盾待脱敏_" 替换为 "_邮盾已脱敏_"
                    string newFileName = fileName.Replace("_邮盾待脱敏_", "_邮盾已脱敏_");
                    string newFullPath = Path.Combine(directoryPath, newFileName);

                    // 重命名文件
                    File.Move(fullPath, newFullPath);

                    // 更新工作表中的文件名
                    activeSheet.Cells[rowIndex, 1].Value = newFileName;

                    MessageBox.Show($"文件名修改成功！新文件路径：{newFullPath}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"文件重命名失败：{ex.Message}，文件路径：{fullPath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }



        #endregion

        #region 脱敏

        public class MaskingRule
        {
            public string Type { get; set; }      // 脱敏规则的类型
            public string Pattern { get; set; }   // 正则表达式模式
            public string Mask { get; set; }      // 掩码格式
            public string Description { get; set; }
        }

        public List<MaskingRule> LoadMaskingRules(string filePath)
        {
            var rules = new List<MaskingRule>();
            var doc = XDocument.Load(filePath);

            foreach (var ruleElement in doc.Descendants("Rule"))
            {
                var rule = new MaskingRule
                {
                    Type = ruleElement.Element("Type")?.Value,
                    Pattern = ruleElement.Element("Pattern")?.Value,
                    Mask = ruleElement.Element("Mask")?.Value,
                    Description = ruleElement.Element("Description")?.Value
                };
                rules.Add(rule);
            }
            return rules;
        }

        private void btnDataMasking_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            Excel.Worksheet activeSheet = app.ActiveSheet;
            int currentColumn = app.ActiveCell.Column;

            // 加载脱敏规则
            string xmlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "DataMaskingRules.xml");
            var maskingRules = LoadMaskingRules(xmlPath);
            if (maskingRules == null || maskingRules.Count == 0)
            {
                MessageBox.Show("未找到脱敏规则，请检查配置文件。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            int firstMatchRow = -1;
            int lastRow = activeSheet.Cells[activeSheet.Rows.Count, currentColumn].End[Excel.XlDirection.xlUp].Row;

            // 检查光标所在列的前8个单元格，寻找第一个符合条件的位置
            for (int i = 1; i <= 8; i++)
            {
                Excel.Range cell = activeSheet.Cells[i, currentColumn];
                // 获取单元格的实际值
                object rawValue = cell.Value2; // 获取未格式化的值
                string cellValue = rawValue?.ToString() ?? string.Empty; // 转为字符串


                // 清理数据：去除空格和不可见字符
                cellValue = cellValue.Trim();
                cellValue = Regex.Replace(cellValue, @"\s+", ""); // 去除多余空格
                foreach (var rule in maskingRules)
                {
                    var regex = new Regex(rule.Pattern);
                    if (regex.IsMatch(cellValue))
                    {
                        firstMatchRow = i;
                        break;
                    }
                }

                if (firstMatchRow != -1) break;
            }

            // 如果没有找到符合的格式，提示用户并返回
            if (firstMatchRow == -1)
            {
                MessageBox.Show("前8个单元格中没有符合脱敏格式的内容。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 从第一个匹配的位置开始，遍历到该列的最后一个有效单元格，进行脱敏处理
            for (int i = firstMatchRow; i <= lastRow; i++)
            {
                Excel.Range cell = activeSheet.Cells[i, currentColumn];
                // 获取单元格实际值
                object rawValue = cell.Value2;
                string cellValue = rawValue?.ToString() ?? string.Empty;

                // 清理数据：移除空格、不可见字符并标准化
                cellValue = Regex.Replace(cellValue, @"\s+", ""); // 移除空格
                cellValue = cellValue.ToUpper(); // 转换为大写

                bool isMasked = false;
                foreach (var rule in maskingRules)
                {
                    var regex = new Regex(rule.Pattern);
                    if (regex.IsMatch(cellValue))
                    {
                        cell.Value = ApplyMask(cellValue, rule.Mask);
                        isMasked = true;
                        break;
                    }
                }

                if (!isMasked)
                {
                    // 不符合的内容标红
                    cell.Interior.Color = Excel.XlRgbColor.rgbRed;
                }
            }

            MessageBox.Show("脱敏处理完成。", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private string ApplyMask(string input, string mask)
        {
            char[] masked = new char[mask.Length];
            int inputIndex = 0;

            for (int i = 0; i < mask.Length && inputIndex < input.Length; i++)
            {
                if (mask[i] == '#')
                {
                    masked[i] = input[inputIndex++];
                }
                else if (mask[i] == '*')
                {
                    masked[i] = '*';
                    inputIndex++;
                }
                else
                {
                    masked[i] = mask[i];  // 对于非 `#` 和 `*` 字符，保持原样
                }
            }

            return new string(masked);
        }


        #endregion

        #region 文件分析
        private void btnAnalyzeExcelFiles_Click(object sender, RibbonControlEventArgs e)
        {
            var app = Globals.ThisAddIn.Application;
            app.ScreenUpdating = false;  // 暂停屏幕更新
            Excel.Worksheet activeSheet = app.ActiveSheet;
            Excel.Range selectedRange = app.Selection;

            // 检查当前工作表名称是否为“搜索结果”或“疑似敏感”
            if (activeSheet.Name != "搜索结果" && activeSheet.Name != "疑似敏感" && activeSheet.Name != "邮盾文件" && activeSheet.Name != "专项搜索")
            {
                app.ScreenUpdating = true; // 恢复屏幕更新，防止界面卡死
                MessageBox.Show("请在 '搜索结果'、'疑似敏感' 或 '邮盾文件' 工作表中使用此功能。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 确保选中的是 A 列的一个或多个单元格
            if (selectedRange.Columns.Count != 1 || selectedRange.Column != 1)
            {
                app.ScreenUpdating = true; // 恢复屏幕更新，防止界面卡死
                MessageBox.Show("请在 A 列选择一个或多个包含文件名的单元格。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查或创建 FileAnalysis 工作表
            var analysisSheet = app.Worksheets.Cast<Excel.Worksheet>().FirstOrDefault(s => s.Name == "文件透视")
                ?? app.Worksheets.Add(After: app.Worksheets[app.Worksheets.Count]);
            analysisSheet.Name = "文件透视";

            // 写入表头到 FileAnalysis
            string[] headers = { "文件名", "文件路径", "Sheet名称", "填充区域", "行数", "列数", "非空单元格数" };
            for (int i = 0; i < headers.Length; i++)
                analysisSheet.Cells[1, i + 1].Value = headers[i];

            int analysisRow = 2; // 从第二行开始写入分析结果

            foreach (Excel.Range selectedCell in selectedRange.Cells)
            {
                int currentRow = selectedCell.Row;

                // 获取文件夹路径（B 列）和文件名（A 列）
                string folderPath = activeSheet.Cells[currentRow, 2]?.Value?.ToString();
                string fileName = selectedCell.Value?.ToString();

                // 确保文件夹路径和文件名都不为空
                if (string.IsNullOrWhiteSpace(folderPath) || string.IsNullOrWhiteSpace(fileName))
                {
                    MessageBox.Show($"第 {currentRow} 行的文件夹路径或文件名为空，跳过该文件。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    continue;
                }

                // 拼接完整的文件路径
                string filePath = Path.Combine(folderPath, fileName);

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"第 {currentRow} 行的文件路径无效或文件不存在，跳过该文件。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    continue;
                }

                // 使用当前 Excel 实例打开文件并进行分析
                Excel.Workbook workbook = null;
                try
                {
                    workbook = app.Workbooks.Open(filePath, ReadOnly: true);
                    foreach (Excel.Worksheet sheet in workbook.Sheets)
                    {
                        // 获取填充区域（包含内容的区域）
                        Excel.Range usedRange = sheet.UsedRange;
                        int rowCount = usedRange.Rows.Count;
                        int colCount = usedRange.Columns.Count;

                        // 统计非空单元格数量
                        int nonEmptyCellCount = usedRange.Cast<Excel.Range>().Count(cell => cell.Value2 != null);

                        // 写入分析信息到 FileAnalysis
                        analysisSheet.Cells[analysisRow, 1].Value = fileName;          // 文件名
                        analysisSheet.Cells[analysisRow, 2].Value = folderPath;          // 文件路径
                        analysisSheet.Cells[analysisRow, 3].Value = sheet.Name;        // Sheet名称
                        analysisSheet.Cells[analysisRow, 4].Value = usedRange.Address; // 填充区域
                        analysisSheet.Cells[analysisRow, 5].Value = rowCount;          // 行数
                        analysisSheet.Cells[analysisRow, 6].Value = colCount;          // 列数
                        analysisSheet.Cells[analysisRow, 7].Value = nonEmptyCellCount; // 非空单元格数

                        analysisRow++;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"无法分析文件 '{filePath}'：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    // 确保关闭并释放 Workbook 对象
                    if (workbook != null)
                    {
                        workbook.Close(false); // 不保存更改
                        Marshal.ReleaseComObject(workbook);
                    }
                }
            }
            // 在数据写入完成后，自动调整 A 到 G 列的宽度
            Excel.Range autofitRange = analysisSheet.Range["A:G"];
            autofitRange.Columns.AutoFit();

            app.ScreenUpdating = true;   // 恢复屏幕更新
            app.Visible = true;          // 恢复 Excel 可见状态

            MessageBox.Show("文件分析完成，结果已写入 “文件透视” 表格。", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        #endregion

        #region 系统信息
        private void btnSearchSysInfo_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var app = Globals.ThisAddIn.Application;
                // 创建或获取表
                Excel.Worksheet worksheet = app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().Any(sheet => sheet.Name == "详细系统信息")
                    ? app.ActiveWorkbook.Worksheets.Cast<Excel.Worksheet>().First(sheet => sheet.Name == "详细系统信息")
                    : (Excel.Worksheet)app.ActiveWorkbook.Worksheets.Add();
                worksheet.Name = "详细系统信息";
                worksheet.Cells.Clear();


                // 设置列名
                worksheet.Cells[1, 1].Value = "信息类别"; // A列 标题
                worksheet.Cells[1, 2].Value = "信息内容"; // B列 内容

                int currentRow = 2; // 数据从第2行开始

                // 设备名称
                worksheet.Cells[currentRow, 1].Value = "设备名称";
                worksheet.Cells[currentRow, 2].Value = Environment.MachineName;
                currentRow++;

                // 处理器信息
                worksheet.Cells[currentRow, 1].Value = "处理器信息";
                using (var searcher = new ManagementObjectSearcher("select Name from Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        worksheet.Cells[currentRow, 2].Value = obj["Name"];
                        currentRow++;
                    }
                }

                // 内存总容量
                worksheet.Cells[currentRow, 1].Value = "内存总容量";
                using (var searcher = new ManagementObjectSearcher("select Capacity from Win32_PhysicalMemory"))
                {
                    long totalMemory = 0;
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        totalMemory += Convert.ToInt64(obj["Capacity"]);
                    }
                    worksheet.Cells[currentRow, 2].Value = $"{totalMemory / (1024 * 1024 * 1024)} GB";
                }
                currentRow++;

                // 系统类型
                worksheet.Cells[currentRow, 1].Value = "系统类型";
                worksheet.Cells[currentRow, 2].Value = Environment.Is64BitOperatingSystem ? "64 位" : "32 位";
                currentRow++;

                // Windows版本
                string osVersion = Environment.OSVersion.VersionString;
                string osName = osVersion.Contains("10.0") ? "Windows 10" :
                                osVersion.Contains("6.3") ? "Windows 8.1" :
                                osVersion.Contains("6.2") ? "Windows 8" :
                                osVersion.Contains("6.1") ? "Windows 7" :
                                osVersion.Contains("11") ? "Windows 11" : "未知版本";
                worksheet.Cells[currentRow, 1].Value = "Windows 版本";
                worksheet.Cells[currentRow, 2].Value = $"{osName} - {osVersion}";
                currentRow++;

                // 设备ID
                worksheet.Cells[currentRow, 1].Value = "设备ID";
                worksheet.Cells[currentRow, 2].Value = $"{Environment.UserDomainName}\\{Environment.UserName}";
                currentRow++;

                // 显卡信息
                worksheet.Cells[currentRow, 1].Value = "显卡信息";
                using (var searcher = new ManagementObjectSearcher("select Name from Win32_VideoController"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        worksheet.Cells[currentRow, 2].Value = obj["Name"];
                        currentRow++;
                    }
                }

                // 硬盘总容量
                worksheet.Cells[currentRow, 1].Value = "硬盘总容量";
                using (var searcher = new ManagementObjectSearcher("select Size from Win32_DiskDrive"))
                {
                    long totalDiskSize = 0;
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        totalDiskSize += Convert.ToInt64(obj["Size"]);
                    }
                    worksheet.Cells[currentRow, 2].Value = $"{totalDiskSize / (1024 * 1024 * 1024)} GB";
                }
                currentRow++;

                // 主板制造商和型号
                worksheet.Cells[currentRow, 1].Value = "主板信息";
                using (var searcher = new ManagementObjectSearcher("select Manufacturer, Product from Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        worksheet.Cells[currentRow, 2].Value = $"{obj["Manufacturer"]} - {obj["Product"]}";
                    }
                }
                currentRow++;

                // BIOS版本
                worksheet.Cells[currentRow, 1].Value = "BIOS版本";
                using (var searcher = new ManagementObjectSearcher("select Version from Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        worksheet.Cells[currentRow, 2].Value = obj["Version"];
                    }
                }
                currentRow++;
                currentRow++;

                // 检测屏幕锁定时间
                worksheet.Cells[currentRow, 1].Value = "屏幕锁定时间";
                using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Control Panel\Desktop"))
                {
                    var screenSaveTimeout = key?.GetValue("ScreenSaveTimeOut")?.ToString();
                    if (int.TryParse(screenSaveTimeout, out int timeoutSeconds) && timeoutSeconds > 0)
                    {
                        int timeoutMinutes = timeoutSeconds / 60;
                        worksheet.Cells[currentRow, 2].Value = $"{timeoutMinutes} 分钟";

                        // 检查屏幕锁定时间是否大于3分钟
                        if (timeoutMinutes > 3)
                        {
                            worksheet.Cells[currentRow, 3].Value = "不合规";
                            worksheet.Cells[currentRow, 3].Font.Color = System.Drawing.Color.Red; // 设置红色字体
                        }
                        else
                        {
                            worksheet.Cells[currentRow, 3].Value = "合规";
                            worksheet.Cells[currentRow, 3].Font.Color = System.Drawing.Color.Green; // 设置红色字体
                        }
                    }
                    else
                    {
                        worksheet.Cells[currentRow, 2].Value = "未设置";
                        worksheet.Cells[currentRow, 3].Value = "不合规";
                        worksheet.Cells[currentRow, 3].Font.Color = System.Drawing.Color.Red; // 设置红色字体
                    }
                }
                currentRow++;

                // 检测是否设置屏保
                worksheet.Cells[currentRow, 1].Value = "是否设置屏幕锁定";
                using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Control Panel\Desktop"))
                {
                    var screensaverPasswordValue = key?.GetValue("ScreenSaverIsSecure")?.ToString();
                    if (screensaverPasswordValue == "1")
                    {
                        worksheet.Cells[currentRow, 2].Value = "已设置";
                        worksheet.Cells[currentRow, 3].Value = "合规";
                        worksheet.Cells[currentRow, 3].Font.Color = System.Drawing.Color.Green; // 设置红色字体
                    }
                    else
                    {
                        worksheet.Cells[currentRow, 2].Value = "未设置";
                        worksheet.Cells[currentRow, 3].Value = "不合规";
                        worksheet.Cells[currentRow, 3].Font.Color = System.Drawing.Color.Red; // 设置红色字体
                    }
                }
                currentRow++;

                worksheet.Cells[currentRow, 1].Value = "IP 地址";
                worksheet.Cells[currentRow, 2].Value = GetLocalIPAddress();
                currentRow++;

                worksheet.Cells[currentRow, 1].Value = "MAC 地址";
                worksheet.Cells[currentRow, 2].Value = GetMachineMacAddress();
                currentRow++;



                worksheet.Columns["A:G"].AutoFit();
                // 操作成功提示
                MessageBox.Show("系统信息搜索完成。", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("读取系统信息时发生错误: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetLocalIPAddress()
        {
            foreach (var nic in NetworkInterface.GetAllNetworkInterfaces())
            {
                // 只获取启用的以太网和无线网络接口的 IPv4 地址
                if (nic.OperationalStatus == OperationalStatus.Up &&
                    (nic.NetworkInterfaceType == NetworkInterfaceType.Ethernet || nic.NetworkInterfaceType == NetworkInterfaceType.Wireless80211))
                {
                    var ipProps = nic.GetIPProperties();
                    foreach (var ip in ipProps.UnicastAddresses)
                    {
                        // 只选择 IPv4 地址，并筛选以 192.168 开头的地址
                        if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork &&
                            ip.Address.ToString().StartsWith("192.168"))
                        {
                            return ip.Address.ToString(); // 返回第一个符合条件的 IP 地址
                        }
                    }
                }
            }

            return "未知IP"; // 如果没有找到符合条件的 IP 地址
        }

        private string GetMachineMacAddress()
        {
            // 遍历所有网络接口，寻找符合条件的物理网卡
            foreach (var nic in NetworkInterface.GetAllNetworkInterfaces())
            {
                // 只选择启用的物理网卡（排除虚拟网卡和空的MAC地址）
                if (nic.OperationalStatus == OperationalStatus.Up &&
                    nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                    nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                    nic.GetPhysicalAddress().GetAddressBytes().Length > 0)
                {
                    // 返回第一个找到的物理网卡的 MAC 地址（格式化为 XX:XX:XX:XX:XX:XX）
                    return string.Join("_", nic.GetPhysicalAddress().GetAddressBytes().Select(b => b.ToString("X2")));
                }
            }

            return "未知MAC地址"; // 如果没有找到符合条件的 MAC 地址
        }
        //// 获取首个符合条件的有效 IP 地址
        //private string GetLocalIPAddress()
        //{
        //    foreach (var nic in NetworkInterface.GetAllNetworkInterfaces())
        //    {
        //        if ((nic.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
        //             nic.NetworkInterfaceType == NetworkInterfaceType.Wireless80211) &&
        //             nic.OperationalStatus == OperationalStatus.Up)
        //        {
        //            var ipProps = nic.GetIPProperties();
        //            foreach (var ip in ipProps.UnicastAddresses)
        //            {
        //                if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork &&
        //                    !IsLinkLocal(ip.Address))
        //                {
        //                    return ip.Address.ToString();
        //                }
        //            }
        //        }
        //    }
        //    return "未知IP"; // 如果没有找到有效 IP
        //}

        // 获取设备IP地址信息


        #endregion

        #region 保存
        private void btnSave_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 获取当前工作簿
                Excel.Workbook currentWorkbook = Globals.ThisAddIn.Application.ActiveWorkbook;

                // 检查是否包含 "疑似敏感" 工作表
                bool hasSensitiveSheet = false;
                foreach (Excel.Worksheet sheet in currentWorkbook.Worksheets)
                {
                    if (sheet.Name == "疑似敏感")
                    {
                        hasSensitiveSheet = true;
                        break;
                    }
                }

                // 如果没有找到 "疑似敏感" 工作表，则提示并终止保存
                if (!hasSensitiveSheet)
                {
                    System.Windows.Forms.MessageBox.Show("未找到“疑似敏感”工作表，无法保存文件。", "保存失败", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning);
                    return;
                }

                // 获取IP地址并生成文件名
                string ipAddress = GetLocalIPAddress();
                // 如果 IP 地址是 "未知IP"，则使用 MAC 地址
                if (ipAddress == "未知IP")
                {
                    ipAddress = GetMachineMacAddress(); // 使用 GetMachineMacAddress 方法获取 MAC 地址
                }
                string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                string fileName = $"数据检查_{ipAddress}_{timestamp}.xlsx";

                // 设置保存路径
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string fullPath = Path.Combine(desktopPath, fileName);

                // 使用写入密码保护文件，未设置打开密码
                currentWorkbook.SaveAs(
                    fullPath,
                    Excel.XlFileFormat.xlOpenXMLWorkbook,
                    Password: "",                // 无需打开密码
                    WriteResPassword: "psbc@1234", // 写入密码
                    ReadOnlyRecommended: true,    // 只读推荐
                    AccessMode: Excel.XlSaveAsAccessMode.xlNoChange
                );

                // 提示保存成功
                System.Windows.Forms.MessageBox.Show($"文件已保存为只读推荐文件并设置了写入密码：\n{fullPath}", "保存成功", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);

                // 打开文件所在文件夹并选中该文件
                Process.Start("explorer.exe", $"/select,\"{fullPath}\"");
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"发生错误：{ex.Message}", "保存失败", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }
        #endregion

        #region 配置
        private void btnConfig_Click(object sender, RibbonControlEventArgs e)
        {
            // 注册表路径和键名
            string registryKeyPath = @"HKEY_CURRENT_USER\Software\WangKai\邮盾";
            string registryValueName = "Path"; // 存储路径的键名

            // 从注册表获取用户指定的安装路径
            string userInstallPath = (string)Registry.GetValue(registryKeyPath, registryValueName, null);

            if (!string.IsNullOrEmpty(userInstallPath) && Directory.Exists(userInstallPath))
            {
                // 打开用户指定的路径
                Process.Start("explorer.exe", userInstallPath);
            }
            else
            {
                MessageBox.Show("用户指定的路径无效或不存在。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region 帮助
        private void btnMenu_Click(object sender, RibbonControlEventArgs e)
        {
            // 注册表路径和键名，和上一个按钮保持一致
            string registryKeyPath = @"HKEY_CURRENT_USER\Software\WangKai\邮盾";
            string registryValueName = "Path"; // 存储路径的键名

            // 从注册表获取用户指定的安装路径
            string userInstallPath = (string)Registry.GetValue(registryKeyPath, registryValueName, null);

            if (!string.IsNullOrEmpty(userInstallPath) && Directory.Exists(userInstallPath))
            {
                // 指定特定的 .rtf 文件名
                string rtfFilePath = Path.Combine(userInstallPath, "使用说明V1.6.pdf");

                if (File.Exists(rtfFilePath))
                {
                    // 使用默认程序打开指定的 .rtf 文件
                    Process.Start("explorer.exe", rtfFilePath);
                }
                else
                {
                    MessageBox.Show("未找到指定的文件 '使用说明.pdf'。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("用户指定的路径无效或不存在。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region 缓存清理
        private void btnDeleteCacheWX_Click(object sender, RibbonControlEventArgs e)
        {
            // 确认缓存路径
            string weChatCachePath = ConfirmCachePath("微信", Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData) + @"\Tencent\WeChat\");
            if (!string.IsNullOrEmpty(weChatCachePath))
            {
                // 清理缓存
                CleanCache("微信", weChatCachePath);
            }
        }

        private void btnDeleteCacheQQ_Click(object sender, RibbonControlEventArgs e)
        {
            // 确认缓存路径
            string qqCachePath = ConfirmCachePath("QQ", @"C:\Users\<USER>\AppData\Roaming\QQ\Cache");
            if (!string.IsNullOrEmpty(qqCachePath))
            {
                // 清理缓存
                CleanCache("QQ", qqCachePath);
            }
        }

        /// <summary>
        /// 从注册表中获取微信的缓存路径
        /// </summary>
        /// <param name="appName">应用名称</param>
        /// <returns>缓存路径</returns>
        private List<string> GetCachePathsFromRegistry(string appName)
        {
            List<string> cachePaths = new List<string>();

            // 判断应用类型
            if (appName == "微信")
            {
                string registryKeyPath = @"SOFTWARE\Tencent\WeChat";
                string valueName = "FileSavePath";  // 微信的注册表键名

                try
                {
                    // 获取微信的注册表路径
                    object registryValue = Registry.CurrentUser.OpenSubKey(registryKeyPath)?.GetValue(valueName);
                    string basePath = registryValue as string;

                    // 判断路径是否为空或无效
                    if (string.IsNullOrEmpty(basePath) || !Directory.Exists(basePath))
                    {
                        // 如果路径无效，手动设置为默认路径
                        basePath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                    }

                    // 如果路径有效，继续查找
                    if (Directory.Exists(basePath))
                    {
                        // 查找所有以 "wxid_" 开头的子文件夹
                        string weChatFilesPath = Path.Combine(basePath, "WeChat Files");
                        if (Directory.Exists(weChatFilesPath))
                        {
                            cachePaths.AddRange(Directory.GetDirectories(weChatFilesPath, "wxid_*"));
                        }
                        else
                        {
                            MessageBox.Show($"目录 '{weChatFilesPath}' 不存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"读取注册表时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else if (appName == "QQ")
            {
                // QQ 缓存路径在 "Documents\Tencent Files" 下，根据 QQ 号查找
                string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                string qqFilesPath = Path.Combine(documentsPath, "Tencent Files");

                // 检查 "Tencent Files" 目录是否存在
                if (Directory.Exists(qqFilesPath))
                {
                    // 查找所有以 QQ 号命名的文件夹
                    var qqFolders = Directory.GetDirectories(qqFilesPath, "*", SearchOption.TopDirectoryOnly);

                    foreach (var qqFolder in qqFolders)
                    {
                        // 获取 FileRecv 子目录路径
                        string fileRecvPath = Path.Combine(qqFolder, "FileRecv");
                        if (Directory.Exists(fileRecvPath))
                        {
                            cachePaths.Add(fileRecvPath);
                        }
                    }
                }
            }

            return cachePaths;
        }







        /// <summary>
        /// 弹出对话框确认或修改缓存路径
        /// </summary>
        /// <param name="appName">应用名称</param>
        /// <param name="defaultPath">默认路径</param>
        /// <returns>最终确认的缓存路径</returns>
        private string ConfirmCachePath(string appName, string defaultPath)
        {
            List<string> cachePaths = GetCachePathsFromRegistry(appName);

            if (cachePaths.Count == 0)
            {
                cachePaths.Add(defaultPath);
            }

            using (System.Windows.Forms.Form form = new System.Windows.Forms.Form())
            {
                form.Width = 490;
                form.Height = 190;
                form.Text = $"{appName} 缓存路径确认";
                form.StartPosition = FormStartPosition.CenterScreen;

                System.Windows.Forms.Label label = new System.Windows.Forms.Label
                {
                    Text = $"请确认 {appName} 的缓存路径：",
                    Left = 10,
                    Top = 20,
                    Width = 450
                };
                form.Controls.Add(label);

                System.Windows.Forms.ComboBox pathComboBox = new System.Windows.Forms.ComboBox
                {
                    Left = 10,
                    Top = 50,
                    Width = 450,
                    DropDownStyle = ComboBoxStyle.DropDownList
                };

                foreach (var path in cachePaths)
                {
                    pathComboBox.Items.Add(path);
                }

                pathComboBox.SelectedIndex = 0;
                form.Controls.Add(pathComboBox);

                System.Windows.Forms.Button okButton = new System.Windows.Forms.Button
                {
                    Text = "确认",
                    Left = 260,
                    Top = 80,
                    Width = 100
                };
                okButton.Click += (sender, e) =>
                {
                    form.DialogResult = DialogResult.OK;
                    form.Close();
                };
                form.Controls.Add(okButton);

                System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button
                {
                    Text = "取消",
                    Left = 360,
                    Top = 80,
                    Width = 100
                };
                cancelButton.Click += (sender, e) =>
                {
                    form.DialogResult = DialogResult.Cancel;
                    form.Close();
                };
                form.Controls.Add(cancelButton);

                // 新增自定义路径按钮
                System.Windows.Forms.Button customButton = new System.Windows.Forms.Button
                {
                    Text = "手动选择路径",
                    Left = 10,
                    Top = 80, // 放在确认和取消按钮下方
                    Width = 200
                };
                customButton.Click += (sender, e) =>
                {
                    // 打开文件夹选择对话框
                    string selectedPath = SelectCustomCachePath();
                    if (!string.IsNullOrEmpty(selectedPath))
                    {
                        // 如果用户选择了路径，更新ComboBox显示选项
                        pathComboBox.Items.Add(selectedPath);
                        pathComboBox.SelectedItem = selectedPath;  // 设置为选中的路径
                    }
                };
                form.Controls.Add(customButton);

                System.Windows.Forms.Label label1 = new System.Windows.Forms.Label
                {
                    Text = "自动识别的路径不是您的缓存路径时，请打开对应的软件“设置”-“文件管理”，手动查询并填写路径",
                    Left = 10,
                    Top = 110,
                    Width = 480,
                    AutoSize = false, // 禁用自动调整大小
                    Height = 40, // 根据需要设置合适的高度，或者稍后使用 MeasureString 计算高度
                    TextAlign = ContentAlignment.TopLeft // 设置文本对齐方式
                };

                // 自动调整 Label 高度以适应多行文本
                using (Graphics g = form.CreateGraphics())
                {
                    SizeF sizeF = g.MeasureString(label1.Text, label1.Font, label1.Width);
                    label1.Height = (int)sizeF.Height + 10; // 增加一点间距
                }

                form.Controls.Add(label1);

                if (form.ShowDialog() == DialogResult.OK)
                {
                    return pathComboBox.SelectedItem.ToString();
                }
                return string.Empty;
            }
        }



        /// <summary>
        /// 清理指定路径的缓存
        /// </summary>
        /// <param name="appName">应用名称</param>
        /// <param name="cachePath">缓存路径</param>
        private void CleanCache(string appName, string cachePath)
        {
            try
            {
                if (Directory.Exists(cachePath))
                {
                    foreach (var file in Directory.GetFiles(cachePath))
                    {
                        File.Delete(file);
                    }
                    foreach (var dir in Directory.GetDirectories(cachePath))
                    {
                        Directory.Delete(dir, true);
                    }
                    MessageBox.Show($"{appName} 缓存已清理！", "清理完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"{appName} 缓存路径无效！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清理 {appName} 缓存时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 新增的按钮点击事件，允许用户自定义缓存路径
        private void btnSetCustomCachePath_Click(object sender, RibbonControlEventArgs e)
        {
            string customCachePath = SelectCustomCachePath();
            if (!string.IsNullOrEmpty(customCachePath))
            {
                // 清理自定义路径下的缓存
                CleanCache("自定义应用", customCachePath);
            }
        }

        // 弹出文件夹选择对话框，允许用户选择自定义路径
        private string SelectCustomCachePath()
        {
            using (System.Windows.Forms.FolderBrowserDialog folderDialog = new System.Windows.Forms.FolderBrowserDialog())
            {
                folderDialog.Description = "请选择缓存文件夹路径";
                folderDialog.SelectedPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);  // 设置默认路径

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    return folderDialog.SelectedPath;
                }
            }
            return string.Empty;  // 如果用户取消，则返回空
        }



        #endregion
    }
}
